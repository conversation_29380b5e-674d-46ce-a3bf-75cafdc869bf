# Firestore Rules Deployment Guide

## Issue
The pricing management system requires access to the `settings` collection in Firestore, but the current security rules don't allow this access, resulting in the error:

```
FirebaseError: Missing or insufficient permissions.
```

## Solution
The Firestore rules have been updated to include access to the `settings` collection for admin users. You need to deploy these updated rules to Firebase.

## Steps to Deploy Updated Rules

### Option 1: Using Firebase Console (Recommended)
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project
3. Navigate to **Firestore Database** → **Rules**
4. Copy the contents of `firestore.rules` from this project
5. Paste it into the rules editor
6. Click **Publish**

### Option 2: Using Firebase CLI
If you have Firebase CLI installed:

```bash
# Install Firebase CLI if not already installed
npm install -g firebase-tools

# Login to Firebase
firebase login

# Initialize Firebase in your project (if not already done)
firebase init firestore

# Deploy the rules
firebase deploy --only firestore:rules
```

## Updated Rules Summary
The key addition to the rules is:

```javascript
// Settings collection - pricing configuration and other app settings
match /settings/{settingId} {
  // Only admins can read and write settings
  allow read, write: if isAdmin();
}
```

## Admin Email Configuration
Make sure your email is listed in the admin emails in both:

1. **firestore.rules** (lines 9-15):
```javascript
function isAdmin() {
  return request.auth != null && (
    request.auth.token.email == '<EMAIL>' ||
    request.auth.token.email == '<EMAIL>' ||
    request.auth.token.email in [
      '<EMAIL>'  // Add your email here
    ]
  );
}
```

2. **src/services/firebase.ts** (lines 166-168):
```javascript
const adminEmails = [
  '<EMAIL>', // Your actual admin email
  // Add more admin emails here
];
```

## Testing the Fix
After deploying the rules:

1. Refresh your application
2. Login as an admin user
3. Navigate to the Admin panel → Pricing tab
4. The pricing configuration should now load successfully
5. If it's the first time, you may see an "Initialize Default Pricing" button - click it to set up the initial configuration

## Troubleshooting

### Still getting permissions error?
- Double-check that your email is in both the Firestore rules and the Firebase service admin emails list
- Make sure you're logged in with the correct admin account
- Verify the rules were deployed successfully in the Firebase Console

### Can't deploy rules?
- Make sure you have the correct permissions on the Firebase project
- Try using the Firebase Console method instead of CLI
- Check that you're connected to the internet and Firebase services are operational

### Default pricing not initializing?
- Click the "Initialize Default Pricing" button in the error screen
- Check the browser console for any additional error messages
- Verify your admin permissions are working by checking other admin functions

## Default Pricing Configuration
The system will initialize with these default values:

**Services:**
- House Cleaning: $150
- Office Cleaning: $200  
- Hotel Cleaning: $300
- Hourly (Customer Supply): $25/hr (2hr minimum)
- Hourly (Company Supply): $35/hr (2hr minimum)
- Backyard Cleaning: $30/hr (2hr minimum)

**Multipliers:**
- Size: Small (1x), Medium (1.5x), Large (2x), Extra Large (3x)
- Frequency: One-time (1x), Weekly (0.85x), Bi-weekly (0.9x), Monthly (0.95x)
- Estimated Hours: Small (2h), Medium (3h), Large (4h), Extra Large (6h)

After initialization, all these values can be modified through the admin interface.
