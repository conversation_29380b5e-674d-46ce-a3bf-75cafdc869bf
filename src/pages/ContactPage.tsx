import React, { useState } from 'react';
import { Mail, Phone, MapPin, Clock, CheckCircle } from 'lucide-react';
import FormInput from '../components/FormInput';
import FormSelect from '../components/FormSelect';
import FormTextarea from '../components/FormTextarea';
import { firebaseService } from '../services/firebase';
import { analyticsService } from '../services/analytics';
import { validateContactForm } from '../utils/validation';
import type { ContactFormData } from '../types';
import type { ValidationError } from '../utils/validation';

const ContactPage: React.FC = () => {
  const [contactForm, setContactForm] = useState<ContactFormData>({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);
  const [errors, setErrors] = useState<ValidationError>({});

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setContactForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Clear previous errors
    setErrors({});
    
    // Validate form
    const validation = validateContactForm(contactForm);
    if (!validation.isValid) {
      setErrors(validation.errors);
      return;
    }
    
    setIsSubmitting(true);

    try {
      await firebaseService.addContactMessage(contactForm);
      
      // Track successful contact form submission
      analyticsService.trackEvent('contact_form', 'form_submission', contactForm.subject);
      
      setSubmitted(true);
    } catch (error) {
      console.error('Error submitting contact form:', error);
      setErrors({ submit: 'There was an error sending your message. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetForm = () => {
    setContactForm({
      name: '',
      email: '',
      phone: '',
      subject: '',
      message: ''
    });
    setSubmitted(false);
    setErrors({});
  };

  if (submitted) {
    return (
      <div className="min-h-screen py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md mx-auto">
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-8 text-center">
            <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Message Sent!</h2>
            <p className="text-gray-600 mb-6">
              Thank you for contacting us. We'll get back to you within 24 hours.
            </p>
            <button
              onClick={resetForm}
              className="px-6 py-2 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 transition-colors"
            >
              Send Another Message
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Contact Us</h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Get in touch with our team. We're here to help with all your cleaning needs.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12">
          {/* Contact Information */}
          <div>
            <h2 className="text-2xl font-semibold text-gray-900 mb-6">Get in Touch</h2>
            
            <div className="space-y-6">
              <div className="flex items-start">
                <Phone className="h-6 w-6 text-green-600 mt-1 mr-3 flex-shrink-0" />
                <div>
                  <h3 className="font-medium text-gray-900">Phone</h3>
                  <p className="text-gray-600">(*************</p>
                  <p className="text-sm text-gray-500">Available 7 days a week</p>
                </div>
              </div>

              <div className="flex items-start">
                <Mail className="h-6 w-6 text-green-600 mt-1 mr-3 flex-shrink-0" />
                <div>
                  <h3 className="font-medium text-gray-900">Email</h3>
                  <p className="text-gray-600"><EMAIL></p>
                  <p className="text-sm text-gray-500">We'll respond within 24 hours</p>
                </div>
              </div>

              <div className="flex items-start">
                <MapPin className="h-6 w-6 text-green-600 mt-1 mr-3 flex-shrink-0" />
                <div>
                  <h3 className="font-medium text-gray-900">Service Area</h3>
                  <p className="text-gray-600">Ottawa, Gatineau & Surrounding Areas</p>
                  <p className="text-sm text-gray-500">Serving all of Ottawa-Gatineau region within 50km</p>
                </div>
              </div>

              <div className="flex items-start">
                <Clock className="h-6 w-6 text-green-600 mt-1 mr-3 flex-shrink-0" />
                <div>
                  <h3 className="font-medium text-gray-900">Business Hours</h3>
                  <p className="text-gray-600">Monday - Friday: 8:00 AM - 6:00 PM</p>
                  <p className="text-gray-600">Saturday - Sunday: 9:00 AM - 5:00 PM</p>
                </div>
              </div>
            </div>

            {/* Emergency Contact */}
            <div className="mt-8 p-6 bg-red-50 rounded-lg border border-red-100">
              <h3 className="font-semibold text-red-900 mb-2">Emergency Services</h3>
              <p className="text-sm text-red-700 mb-2">
                For urgent cleaning needs (flood, damage, etc.)
              </p>
              <p className="font-medium text-red-900">438 (454) 0470</p>
              <p className="text-xs text-red-600">Available 24/7</p>
            </div>
          </div>

          {/* Contact Form */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-6">Send us a Message</h2>
            
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid md:grid-cols-2 gap-4">
                <FormInput
                  label="Full Name"
                  name="name"
                  value={contactForm.name}
                  onChange={handleInputChange}
                  required
                  placeholder="John Doe"
                  error={errors.name}
                />
                <FormInput
                  label="Email Address"
                  name="email"
                  type="email"
                  value={contactForm.email}
                  onChange={handleInputChange}
                  required
                  placeholder="<EMAIL>"
                  error={errors.email}
                />
              </div>

              <FormInput
                label="Phone Number"
                name="phone"
                type="tel"
                value={contactForm.phone}
                onChange={handleInputChange}
                placeholder="(*************"
                error={errors.phone}
              />

              <FormSelect
                label="Subject"
                name="subject"
                value={contactForm.subject}
                onChange={handleInputChange}
                required
                error={errors.subject}
              >
                <option value="">Select a subject</option>
                <option value="general">General Inquiry</option>
                <option value="quote">Request Quote</option>
                <option value="scheduling">Scheduling</option>
                <option value="complaint">Complaint</option>
                <option value="compliment">Compliment</option>
                <option value="other">Other</option>
              </FormSelect>

              <FormTextarea
                label="Message"
                name="message"
                value={contactForm.message}
                onChange={handleInputChange}
                rows={5}
                required
                placeholder="Tell us how we can help you..."
                error={errors.message}
              />

              {errors.submit && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-sm text-red-600">{errors.submit}</p>
                </div>
              )}

              <button
                type="submit"
                disabled={isSubmitting}
                className="w-full bg-green-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isSubmitting ? 'Sending...' : 'Send Message'}
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContactPage;
