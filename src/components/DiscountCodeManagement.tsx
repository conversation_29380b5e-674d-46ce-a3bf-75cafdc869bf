import React, { useState, useEffect } from 'react';
import {
  Tag,
  Plus,
  Edit3,
  Trash2,
  Save,
  X,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Eye,
  EyeOff
} from 'lucide-react';
import { firebaseService } from '../services/firebase';
import type { DiscountCodeWithId } from '../types';
import LoadingSpinner from './LoadingSpinner';

interface DiscountCodeManagementProps {
  onUpdate?: () => void;
}

interface Toast {
  message: string;
  type: 'success' | 'error';
}

const DiscountCodeManagement: React.FC<DiscountCodeManagementProps> = ({ onUpdate }) => {
  const [discountCodes, setDiscountCodes] = useState<DiscountCodeWithId[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [editingCode, setEditingCode] = useState<string | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [toast, setToast] = useState<Toast | null>(null);
  
  // Form state
  const [formData, setFormData] = useState({
    code: '',
    percentage: 0,
    active: true
  });

  useEffect(() => {
    loadDiscountCodes();
  }, []);

  useEffect(() => {
    if (toast) {
      const timer = setTimeout(() => setToast(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [toast]);

  const loadDiscountCodes = async () => {
    setLoading(true);
    try {
      const codes = await firebaseService.admin.getDiscountCodes();
      setDiscountCodes(codes as DiscountCodeWithId[]);
    } catch (error) {
      console.error('Error loading discount codes:', error);
      setToast({ message: 'Failed to load discount codes', type: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({ code: '', percentage: 0, active: true });
    setShowCreateForm(false);
    setEditingCode(null);
  };

  const handleCreate = async () => {
    if (!formData.code.trim() || formData.percentage <= 0 || formData.percentage > 100) {
      setToast({ message: 'Please enter a valid code and percentage (1-100)', type: 'error' });
      return;
    }

    setSaving(true);
    try {
      await firebaseService.admin.createDiscountCode(
        formData.code.trim().toUpperCase(),
        formData.percentage,
        formData.active
      );
      await loadDiscountCodes();
      resetForm();
      setToast({ message: 'Discount code created successfully', type: 'success' });
      onUpdate?.();
    } catch (error) {
      console.error('Error creating discount code:', error);
      setToast({ message: 'Failed to create discount code', type: 'error' });
    } finally {
      setSaving(false);
    }
  };

  const handleUpdate = async (code: string) => {
    const codeData = discountCodes.find(c => c.code === code);
    if (!codeData) return;

    setSaving(true);
    try {
      await firebaseService.admin.updateDiscountCode(code, {
        percentage: codeData.percentage,
        active: codeData.active
      });
      setEditingCode(null);
      setToast({ message: 'Discount code updated successfully', type: 'success' });
      onUpdate?.();
    } catch (error) {
      console.error('Error updating discount code:', error);
      setToast({ message: 'Failed to update discount code', type: 'error' });
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async (code: string) => {
    if (!confirm(`Are you sure you want to delete the discount code "${code}"?`)) {
      return;
    }

    setSaving(true);
    try {
      await firebaseService.admin.deleteDiscountCode(code);
      await loadDiscountCodes();
      setToast({ message: 'Discount code deleted successfully', type: 'success' });
      onUpdate?.();
    } catch (error) {
      console.error('Error deleting discount code:', error);
      setToast({ message: 'Failed to delete discount code', type: 'error' });
    } finally {
      setSaving(false);
    }
  };

  const updateDiscountCode = (code: string, field: keyof DiscountCodeWithId, value: any) => {
    setDiscountCodes(codes =>
      codes.map(c => c.code === code ? { ...c, [field]: value } : c)
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 flex items-center">
            <Tag className="h-8 w-8 text-green-600 mr-3" />
            Discount Code Management
          </h2>
          <p className="text-gray-600 mt-1">Create and manage discount codes for customers</p>
        </div>
        <button
          onClick={() => setShowCreateForm(true)}
          className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Discount Code
        </button>
      </div>

      {/* Toast Notification */}
      {toast && (
        <div className={`flex items-center p-4 rounded-lg ${
          toast.type === 'success' ? 'bg-green-50 text-green-700' : 'bg-red-50 text-red-700'
        }`}>
          {toast.type === 'success' ? (
            <CheckCircle className="h-5 w-5 mr-2" />
          ) : (
            <AlertCircle className="h-5 w-5 mr-2" />
          )}
          {toast.message}
        </div>
      )}

      {/* Create Form */}
      {showCreateForm && (
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Create New Discount Code</h3>
            <button
              onClick={resetForm}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Code
              </label>
              <input
                type="text"
                value={formData.code}
                onChange={(e) => setFormData({ ...formData, code: e.target.value.toUpperCase() })}
                placeholder="e.g., SAVE20"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Discount Percentage
              </label>
              <input
                type="number"
                min="1"
                max="100"
                value={formData.percentage}
                onChange={(e) => setFormData({ ...formData, percentage: parseInt(e.target.value) || 0 })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
            
            <div className="flex items-end">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.active}
                  onChange={(e) => setFormData({ ...formData, active: e.target.checked })}
                  className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                />
                <span className="ml-2 text-sm text-gray-700">Active</span>
              </label>
            </div>
          </div>
          
          <div className="flex justify-end mt-4 space-x-3">
            <button
              onClick={resetForm}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleCreate}
              disabled={saving}
              className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
            >
              {saving ? (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              {saving ? 'Creating...' : 'Create Code'}
            </button>
          </div>
        </div>
      )}

      {/* Discount Codes Table */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Existing Discount Codes</h3>
        </div>

        {discountCodes.length === 0 ? (
          <div className="p-8 text-center">
            <Tag className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Discount Codes</h3>
            <p className="text-gray-500">Create your first discount code to get started.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Code
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Discount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Created
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {discountCodes.map((code) => (
                  <tr key={code.code} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <Tag className="h-4 w-4 text-green-600 mr-2" />
                        <span className="text-sm font-medium text-gray-900">{code.code}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {editingCode === code.code ? (
                        <input
                          type="number"
                          min="1"
                          max="100"
                          value={code.percentage}
                          onChange={(e) => updateDiscountCode(code.code, 'percentage', parseInt(e.target.value) || 0)}
                          className="w-20 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-green-500 focus:border-transparent"
                        />
                      ) : (
                        <span className="text-sm text-gray-900">{code.percentage}%</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {editingCode === code.code ? (
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            checked={code.active}
                            onChange={(e) => updateDiscountCode(code.code, 'active', e.target.checked)}
                            className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                          />
                          <span className="ml-2 text-sm text-gray-700">Active</span>
                        </label>
                      ) : (
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          code.active
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {code.active ? (
                            <>
                              <Eye className="h-3 w-3 mr-1" />
                              Active
                            </>
                          ) : (
                            <>
                              <EyeOff className="h-3 w-3 mr-1" />
                              Inactive
                            </>
                          )}
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {code.createdAt ? new Date(code.createdAt).toLocaleDateString() : 'N/A'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center space-x-2">
                        {editingCode === code.code ? (
                          <>
                            <button
                              onClick={() => handleUpdate(code.code)}
                              disabled={saving}
                              className="text-green-600 hover:text-green-900 disabled:opacity-50"
                            >
                              {saving ? (
                                <RefreshCw className="h-4 w-4 animate-spin" />
                              ) : (
                                <Save className="h-4 w-4" />
                              )}
                            </button>
                            <button
                              onClick={() => setEditingCode(null)}
                              className="text-gray-600 hover:text-gray-900"
                            >
                              <X className="h-4 w-4" />
                            </button>
                          </>
                        ) : (
                          <>
                            <button
                              onClick={() => setEditingCode(code.code)}
                              className="text-blue-600 hover:text-blue-900"
                            >
                              <Edit3 className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => handleDelete(code.code)}
                              disabled={saving}
                              className="text-red-600 hover:text-red-900 disabled:opacity-50"
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          </>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default DiscountCodeManagement;
