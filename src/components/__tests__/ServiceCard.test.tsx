import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import ServiceCard from '../ServiceCard';
import { Home, Clock } from 'lucide-react';
import type { Service } from '../../types';

// Mock the Link component since we're testing in isolation
vi.mock('react-router-dom', () => ({
  Link: ({ children, to }: { children: React.ReactNode; to: string }) => (
    <a href={to}>{children}</a>
  ),
}));

describe('ServiceCard Component', () => {
  const mockFixedService: Service = {
    id: 'house',
    name: 'House Cleaning',
    icon: Home,
    description: 'Complete residential cleaning for your home',
    features: ['Deep cleaning', 'Regular maintenance', 'Move-in/out cleaning'],
    basePrice: 150
  };

  const mockHourlyService: Service = {
    id: 'hourly-customer-supply',
    name: 'Hourly Cleaning (Customer Supplies)',
    icon: Clock,
    description: 'Flexible hourly cleaning service',
    features: ['Flexible scheduling', 'Bring your own supplies', 'Minimum 2 hours'],
    basePrice: 25,
    isHourly: true,
    rate: 25
  };

  describe('Default Variant', () => {
    it('should render service information correctly', () => {
      render(<ServiceCard service={mockFixedService} />);

      expect(screen.getByText('House Cleaning')).toBeInTheDocument();
      expect(screen.getByText('Complete residential cleaning for your home')).toBeInTheDocument();
      expect(screen.getByText('$150')).toBeInTheDocument();
      expect(screen.getByText('Starting price')).toBeInTheDocument();
    });

    it('should render all features', () => {
      render(<ServiceCard service={mockFixedService} />);

      mockFixedService.features.forEach(feature => {
        expect(screen.getByText(feature)).toBeInTheDocument();
      });
    });

    it('should render get quote button', () => {
      render(<ServiceCard service={mockFixedService} />);

      const quoteButton = screen.getByText('Get Quote');
      expect(quoteButton).toBeInTheDocument();
      expect(quoteButton.closest('a')).toHaveAttribute('href', '/quote');
    });

    it('should display hourly rate for hourly services', () => {
      render(<ServiceCard service={mockHourlyService} />);

      // Check if the price components are displayed
      const priceContainer = document.querySelector('.text-2xl.font-bold.text-green-600');
      expect(priceContainer).toBeInTheDocument();
      expect(priceContainer?.textContent).toContain('$25/hr');
      expect(screen.getByText('Hourly rate')).toBeInTheDocument();
    });

    it('should include service icon', () => {
      render(<ServiceCard service={mockFixedService} />);
      
      // Check if icon is rendered by looking for the SVG element directly
      const svgElement = document.querySelector('svg');
      expect(svgElement).toBeInTheDocument();
    });
  });

  describe('Minimal Variant', () => {
    it('should render minimal service card', () => {
      render(<ServiceCard service={mockFixedService} variant="minimal" />);

      expect(screen.getByText('House Cleaning')).toBeInTheDocument();
      expect(screen.getByText('Complete residential cleaning for your home')).toBeInTheDocument();
      expect(screen.getByText('Starting from $150')).toBeInTheDocument();
    });

    it('should not render features in minimal variant', () => {
      render(<ServiceCard service={mockFixedService} variant="minimal" />);

      mockFixedService.features.forEach(feature => {
        expect(screen.queryByText(feature)).not.toBeInTheDocument();
      });
    });

    it('should not render get quote button in minimal variant', () => {
      render(<ServiceCard service={mockFixedService} variant="minimal" />);

      expect(screen.queryByText('Get Quote')).not.toBeInTheDocument();
    });

    it('should display hourly rate correctly in minimal variant', () => {
      render(<ServiceCard service={mockHourlyService} variant="minimal" />);

      expect(screen.getByText('$25/hour')).toBeInTheDocument();
      expect(screen.queryByText('Starting from')).not.toBeInTheDocument();
    });
  });

  describe('Service Type Detection', () => {
    it('should detect fixed pricing services', () => {
      render(<ServiceCard service={mockFixedService} />);
      expect(screen.getByText('$150')).toBeInTheDocument();
      expect(screen.queryByText('$150/hour')).not.toBeInTheDocument();
    });

    it('should detect hourly services and display correctly', () => {
      render(<ServiceCard service={mockHourlyService} variant="minimal" />);
      expect(screen.getByText('$25/hour')).toBeInTheDocument();
    });

    it('should handle different hourly service types', () => {
      const companySupplyService: Service = {
        id: 'hourly-company-supply',
        name: 'Hourly Cleaning (Company Supplies)',
        icon: Clock,
        description: 'Hourly service with supplies',
        features: ['Professional supplies'],
        basePrice: 35,
        isHourly: true,
        rate: 35
      };

      render(<ServiceCard service={companySupplyService} variant="minimal" />);
      expect(screen.getByText('$35/hour')).toBeInTheDocument();
    });

    it('should handle backyard hourly service', () => {
      const backyardService: Service = {
        id: 'backyard-hourly',
        name: 'Backyard Cleaning',
        icon: Clock,
        description: 'Outdoor cleaning service',
        features: ['Patio cleaning'],
        basePrice: 30
      };

      render(<ServiceCard service={backyardService} variant="minimal" />);
      expect(screen.getByText('$30/hour')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper semantic structure', () => {
      render(<ServiceCard service={mockFixedService} />);

      expect(screen.getByRole('heading', { level: 3 })).toHaveTextContent('House Cleaning');
      expect(screen.getByRole('link')).toHaveTextContent('Get Quote');
    });

    it('should have accessible button text', () => {
      render(<ServiceCard service={mockFixedService} />);

      const quoteButton = screen.getByRole('link', { name: 'Get Quote' });
      expect(quoteButton).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('should handle services with no features', () => {
      const serviceWithNoFeatures: Service = {
        ...mockFixedService,
        features: []
      };

      render(<ServiceCard service={serviceWithNoFeatures} />);
      expect(screen.getByText('House Cleaning')).toBeInTheDocument();
    });

    it('should handle very long service names and descriptions', () => {
      const longNameService: Service = {
        ...mockFixedService,
        name: 'Very Long Service Name That Might Wrap To Multiple Lines',
        description: 'This is a very long description that might wrap to multiple lines and should still be displayed correctly without breaking the layout'
      };

      render(<ServiceCard service={longNameService} />);
      expect(screen.getByText(longNameService.name)).toBeInTheDocument();
      expect(screen.getByText(longNameService.description)).toBeInTheDocument();
    });

    it('should handle zero price services', () => {
      const freeService: Service = {
        ...mockFixedService,
        basePrice: 0
      };

      render(<ServiceCard service={freeService} />);
      expect(screen.getByText('$0')).toBeInTheDocument();
    });

    it('should handle high price services', () => {
      const expensiveService: Service = {
        ...mockFixedService,
        basePrice: 9999
      };

      render(<ServiceCard service={expensiveService} />);
      expect(screen.getByText('$9999')).toBeInTheDocument();
    });
  });

  describe('Visual States', () => {
    it('should apply correct CSS classes for default variant', () => {
      const { container } = render(<ServiceCard service={mockFixedService} />);
      
      const cardElement = container.querySelector('.bg-white.rounded-xl.shadow-sm');
      expect(cardElement).toBeInTheDocument();
    });

    it('should apply correct CSS classes for minimal variant', () => {
      const { container } = render(<ServiceCard service={mockFixedService} variant="minimal" />);
      
      const cardElement = container.querySelector('.bg-white.rounded-xl.p-6.shadow-sm');
      expect(cardElement).toBeInTheDocument();
    });
  });
});
