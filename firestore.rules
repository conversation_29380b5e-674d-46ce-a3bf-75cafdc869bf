rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    
    // Helper function to check if user is admin
    function isAdmin() {
      return request.auth != null && (
        request.auth.token.email == '<EMAIL>' ||
        request.auth.token.email == '<EMAIL>' ||
        request.auth.token.email in [
          '<EMAIL>'
          // Add more admin emails here as needed
        ]
      );
    }

    // Helper function to check if user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }

    // Helper function to validate quote data structure
    function isValidQuote(data) {
      return data.keys().hasAll(['name', 'email', 'phone', 'serviceType', 'propertySize', 'frequency', 'message']) &&
             data.name is string && data.name.size() > 0 &&
             data.email is string && data.email.size() > 0 &&
             data.phone is string && data.phone.size() > 0 &&
             data.serviceType is string && data.serviceType.size() > 0 &&
             data.propertySize is string &&
             data.frequency is string &&
             data.message is string;
    }

    // Helper function to validate contact data structure
    function isValidContact(data) {
      return data.keys().hasAll(['name', 'email', 'phone', 'subject', 'message']) &&
             data.name is string && data.name.size() > 0 &&
             data.email is string && data.email.size() > 0 &&
             data.phone is string && data.phone.size() > 0 &&
             data.subject is string && data.subject.size() > 0 &&
             data.message is string && data.message.size() > 0;
    }

    // Quotes collection - customers can create, admins can read/update/delete
    match /quotes/{quoteId} {
      // Anyone can create a quote (for contact forms from website visitors)
      allow create: if isValidQuote(request.resource.data);
      
      // Only admins can read, update, and delete quotes
      allow read, update, delete: if isAdmin();
    }

    // Contacts collection - customers can create, admins can read/update/delete
    match /contacts/{contactId} {
      // Anyone can create a contact message (for contact forms from website visitors)
      allow create: if isValidContact(request.resource.data);
      
      // Only admins can read, update, and delete contacts
      allow read, update, delete: if isAdmin();
    }

    // Discount codes collection - read-only for everyone, full access for admins
    match /discountCodes/{codeId} {
      // Anyone can read discount codes to apply them (no authentication required)
      allow read: if true;
      
      // Only admins can create, update, and delete discount codes
      allow write: if isAdmin();
    }

    // Analytics collections for real-time dashboard
    match /page_analytics/{analyticsId} {
      // Anyone can create page analytics (for tracking page views)
      allow create: if true;
      
      // Only admins can read analytics data
      allow read: if isAdmin();
      
      // System can delete old analytics data
      allow delete: if true;
    }

    match /active_users/{sessionId} {
      // Anyone can create/update active user sessions (for real-time tracking)
      allow create, update: if true;
      
      // Only admins can read active users data
      allow read: if isAdmin();
      
      // System can delete old session data
      allow delete: if true;
    }

    // Settings collection - pricing configuration and other app settings
    match /settings/{settingId} {
      // Only admins can read and write settings
      allow read, write: if isAdmin();
    }

    // Admin-specific collections (analytics, settings, etc.)
    match /admin/{document=**} {
      // Only admins have full access to admin collections
      allow read, write: if isAdmin();
    }

    // User profiles collection (if needed in the future)
    match /users/{userId} {
      // Users can read/write their own profile
      allow read, write: if isAuthenticated() && request.auth.uid == userId;
      
      // Admins can read all user profiles
      allow read: if isAdmin();
    }

    // Default deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}