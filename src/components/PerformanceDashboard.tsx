import React, { useState, useEffect } from 'react';
import { BarChart3, Clock, Zap, Activity, TrendingUp } from 'lucide-react';

interface PerformanceData {
  fcp: number;
  lcp: number;
  cls: number;
  fid: number;
  ttfb: number;
  score: number;
}

const PerformanceDashboard: React.FC = () => {
  const [data, setData] = useState<PerformanceData | null>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Only show in development or when explicitly enabled via localStorage
    const isDevelopment = process.env.NODE_ENV === 'development';
    const isExplicitlyEnabled = typeof window !== 'undefined' &&
                               localStorage.getItem('show-perf-dashboard') === 'true';

    if (isDevelopment || isExplicitlyEnabled) {
      collectPerformanceData();
      setIsVisible(true);
    }
  }, []);

  const collectPerformanceData = () => {
    if (typeof window === 'undefined' || !window.performance) return;

    setTimeout(() => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      const paintEntries = performance.getEntriesByType('paint');
      
      const fcp = paintEntries.find(entry => entry.name === 'first-contentful-paint')?.startTime || 0;
      const ttfb = navigation.responseStart - navigation.requestStart;
      
      // Calculate a simple performance score
      const score = Math.max(0, 100 - (fcp / 50) - (ttfb / 20));

      setData({
        fcp,
        lcp: 0, // Will be updated by observer
        cls: 0, // Will be updated by observer
        fid: 0, // Will be updated by observer
        ttfb,
        score: Math.round(score)
      });
    }, 2000);
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600 bg-green-50';
    if (score >= 70) return 'text-yellow-600 bg-yellow-50';
    return 'text-red-600 bg-red-50';
  };

  const getMetricColor = (value: number, thresholds: { good: number; poor: number }) => {
    if (value <= thresholds.good) return 'text-green-600';
    if (value <= thresholds.poor) return 'text-yellow-600';
    return 'text-red-600';
  };

  const formatTime = (ms: number) => {
    if (ms < 1000) return `${ms.toFixed(0)}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  if (!isVisible || !data) return null;

  return (
    <div className="fixed top-4 right-4 z-50 bg-white rounded-lg shadow-lg border border-gray-200 p-4 max-w-xs">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center">
          <BarChart3 className="h-4 w-4 text-blue-600 mr-2" />
          <h3 className="text-sm font-semibold text-gray-900">Performance</h3>
        </div>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-400 hover:text-gray-600 text-sm"
          aria-label="Close performance dashboard"
        >
          ×
        </button>
      </div>

      {/* Performance Score */}
      <div className={`rounded-lg p-3 mb-3 ${getScoreColor(data.score)}`}>
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Performance Score</span>
          <span className="text-lg font-bold">{data.score}/100</span>
        </div>
      </div>

      {/* Core Web Vitals */}
      <div className="space-y-2 text-xs">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Clock className="h-3 w-3 mr-1 text-gray-500" />
            <span className="text-gray-600">FCP</span>
          </div>
          <span className={`font-mono ${getMetricColor(data.fcp, { good: 1800, poor: 3000 })}`}>
            {formatTime(data.fcp)}
          </span>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Zap className="h-3 w-3 mr-1 text-gray-500" />
            <span className="text-gray-600">LCP</span>
          </div>
          <span className={`font-mono ${getMetricColor(data.lcp, { good: 2500, poor: 4000 })}`}>
            {data.lcp > 0 ? formatTime(data.lcp) : 'Measuring...'}
          </span>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Activity className="h-3 w-3 mr-1 text-gray-500" />
            <span className="text-gray-600">TTFB</span>
          </div>
          <span className={`font-mono ${getMetricColor(data.ttfb, { good: 800, poor: 1800 })}`}>
            {formatTime(data.ttfb)}
          </span>
        </div>

        <div className="flex items-center justify-between">
          <span className="text-gray-600">CLS</span>
          <span className={`font-mono ${getMetricColor(data.cls * 1000, { good: 100, poor: 250 })}`}>
            {data.cls > 0 ? data.cls.toFixed(3) : 'Measuring...'}
          </span>
        </div>

        <div className="flex items-center justify-between">
          <span className="text-gray-600">FID</span>
          <span className={`font-mono ${getMetricColor(data.fid, { good: 100, poor: 300 })}`}>
            {data.fid > 0 ? formatTime(data.fid) : 'Waiting...'}
          </span>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="mt-3 pt-2 border-t border-gray-100">
        <div className="flex space-x-2">
          <button
            onClick={() => window.location.reload()}
            className="flex-1 text-xs bg-blue-50 text-blue-600 px-2 py-1 rounded hover:bg-blue-100"
          >
            Refresh
          </button>
          <button
            onClick={() => {
              if (navigator.share) {
                navigator.share({
                  title: 'Performance Report',
                  text: `Performance Score: ${data.score}/100\nFCP: ${formatTime(data.fcp)}\nTTFB: ${formatTime(data.ttfb)}`
                });
              }
            }}
            className="flex-1 text-xs bg-gray-50 text-gray-600 px-2 py-1 rounded hover:bg-gray-100"
          >
            Share
          </button>
        </div>
      </div>

      {/* Tips */}
      <div className="mt-2 text-xs text-gray-500">
        <div className="flex items-center">
          <TrendingUp className="h-3 w-3 mr-1" />
          <span>Real-time monitoring active</span>
        </div>
      </div>
    </div>
  );
};

export default PerformanceDashboard;
