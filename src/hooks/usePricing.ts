import { useState, useEffect } from 'react';
import { firebaseService } from '../services/firebase';
import type { PricingConfiguration, ServicePricing, PricingMultipliers } from '../types';

interface UsePricingReturn {
  pricingConfig: PricingConfiguration | null;
  loading: boolean;
  error: string | null;
  refreshPricing: () => Promise<void>;
  getServicePricing: (serviceId: string) => ServicePricing | null;
  getPricingMultipliers: () => PricingMultipliers | null;
}

// Default pricing configuration as fallback - these should match what's in Firestore
const defaultPricingConfig: Omit<PricingConfiguration, 'lastUpdated' | 'updatedBy'> = {
  services: [
    { id: 'house', name: 'House Cleaning', basePrice: 250, isHourly: false },
    { id: 'office', name: 'Office Cleaning', basePrice: 200, isHourly: false },
    { id: 'hotel', name: 'Hotel Cleaning', basePrice: 300, isHourly: false },
    { id: 'hourly-customer-supply', name: 'Hourly (Customer Supply)', basePrice: 25, isHourly: true, rate: 25, minHours: 2 },
    { id: 'hourly-company-supply', name: 'Hourly (Company Supply)', basePrice: 35, isHourly: true, rate: 35, minHours: 2 },
    { id: 'backyard-hourly', name: 'Backyard Cleaning (Hourly)', basePrice: 30, isHourly: true, rate: 30, minHours: 2 }
  ],
  multipliers: {
    sizeMultipliers: { small: 1, medium: 1.5, large: 2, xlarge: 3 },
    frequencyDiscounts: { 'one-time': 1, weekly: 0.85, 'bi-weekly': 0.9, monthly: 0.95 },
    estimatedHours: { small: 2, medium: 3, large: 4, xlarge: 6 }
  }
};

export const usePricing = (): UsePricingReturn => {
  const [pricingConfig, setPricingConfig] = useState<PricingConfiguration | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadPricingConfiguration = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const config = await firebaseService.admin.getPricingConfiguration();
      
      if (config) {
        setPricingConfig(config);
      } else {
        // Use default configuration if none exists in Firebase
        const defaultConfig: PricingConfiguration = {
          ...defaultPricingConfig,
          lastUpdated: new Date().toISOString(),
          updatedBy: 'system'
        };
        setPricingConfig(defaultConfig);
      }
    } catch (err) {
      console.error('Error loading pricing configuration:', err);
      setError('Failed to load pricing configuration');
      
      // Fallback to default configuration
      const defaultConfig: PricingConfiguration = {
        ...defaultPricingConfig,
        lastUpdated: new Date().toISOString(),
        updatedBy: 'system'
      };
      setPricingConfig(defaultConfig);
    } finally {
      setLoading(false);
    }
  };

  const refreshPricing = async () => {
    await loadPricingConfiguration();
  };

  const getServicePricing = (serviceId: string): ServicePricing | null => {
    if (!pricingConfig) return null;
    return pricingConfig.services.find(service => service.id === serviceId) || null;
  };

  const getPricingMultipliers = (): PricingMultipliers | null => {
    return pricingConfig?.multipliers || null;
  };

  useEffect(() => {
    loadPricingConfiguration();
  }, []);

  return {
    pricingConfig,
    loading,
    error,
    refreshPricing,
    getServicePricing,
    getPricingMultipliers
  };
};

// Singleton instance for global access
let globalPricingConfig: PricingConfiguration | null = null;

export const getGlobalPricingConfig = async (): Promise<PricingConfiguration> => {
  if (globalPricingConfig) {
    return globalPricingConfig;
  }

  try {
    const config = await firebaseService.admin.getPricingConfiguration();
    
    if (config) {
      globalPricingConfig = config;
      return config;
    } else {
      // Return default configuration
      const defaultConfig: PricingConfiguration = {
        ...defaultPricingConfig,
        lastUpdated: new Date().toISOString(),
        updatedBy: 'system'
      };
      globalPricingConfig = defaultConfig;
      return defaultConfig;
    }
  } catch (error) {
    console.error('Error loading global pricing configuration:', error);
    
    // Fallback to default configuration
    const defaultConfig: PricingConfiguration = {
      ...defaultPricingConfig,
      lastUpdated: new Date().toISOString(),
      updatedBy: 'system'
    };
    globalPricingConfig = defaultConfig;
    return defaultConfig;
  }
};

export const clearGlobalPricingConfig = () => {
  globalPricingConfig = null;
};
