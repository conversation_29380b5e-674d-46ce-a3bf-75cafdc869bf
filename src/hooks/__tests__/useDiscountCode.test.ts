import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useDiscountCode } from '../useDiscountCode';

// Mock the firebase service
vi.mock('../../services/firebase', () => ({
  firebaseService: {
    getDiscountCode: vi.fn(),
  },
}));

import { firebaseService } from '../../services/firebase';

describe('useDiscountCode Hook', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should initialize with default values', () => {
    const { result } = renderHook(() => useDiscountCode());

    expect(result.current.discount).toBeNull();
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBeNull();
  });

  it('should apply valid discount code', async () => {
    const mockDiscount = {
      percentage: 10,
      active: true,
    };

    vi.mocked(firebaseService.getDiscountCode).mockResolvedValue(mockDiscount);

    const { result } = renderHook(() => useDiscountCode());

    await act(async () => {
      await result.current.applyDiscountCode('SAVE10');
    });

    expect(result.current.discount).toEqual(mockDiscount);
    expect(result.current.error).toBeNull();
    expect(firebaseService.getDiscountCode).toHaveBeenCalledWith('SAVE10');
  });

  it('should handle invalid discount code', async () => {
    vi.mocked(firebaseService.getDiscountCode).mockResolvedValue(null);

    const { result } = renderHook(() => useDiscountCode());

    await act(async () => {
      await result.current.applyDiscountCode('INVALID');
    });

    expect(result.current.discount).toBeNull();
    expect(result.current.error).toBe('Invalid discount code');
  });

  it('should handle inactive discount code', async () => {
    const mockDiscount = {
      percentage: 20,
      active: false,
    };

    vi.mocked(firebaseService.getDiscountCode).mockResolvedValue(mockDiscount);

    const { result } = renderHook(() => useDiscountCode());

    await act(async () => {
      await result.current.applyDiscountCode('EXPIRED');
    });

    expect(result.current.discount).toBeNull();
    expect(result.current.error).toBe('This discount code has expired');
  });

  it('should handle network errors', async () => {
    vi.mocked(firebaseService.getDiscountCode).mockRejectedValue(new Error('Network error'));

    const { result } = renderHook(() => useDiscountCode());

    await act(async () => {
      await result.current.applyDiscountCode('NETWORK_ERROR');
    });

    expect(result.current.discount).toBeNull();
    expect(result.current.error).toBe('Failed to validate discount code');
  });

  it('should show loading state during validation', async () => {
    let resolvePromise: (value: { percentage: number; active: boolean } | null) => void;
    const promise = new Promise<{ percentage: number; active: boolean } | null>(resolve => {
      resolvePromise = resolve;
    });

    vi.mocked(firebaseService.getDiscountCode).mockReturnValue(promise);

    const { result } = renderHook(() => useDiscountCode());

    // Start applying discount code
    act(() => {
      result.current.applyDiscountCode('LOADING_TEST');
    });

    expect(result.current.isLoading).toBe(true);

    // Resolve the promise
    await act(async () => {
      resolvePromise({ percentage: 15, active: true });
      await promise;
    });

    expect(result.current.isLoading).toBe(false);
  });

  it('should clear discount code', () => {
    const { result } = renderHook(() => useDiscountCode());

    // First apply a discount
    act(() => {
      result.current.applyDiscountCode('TEST').then(() => {
        // Then clear it
        result.current.clearDiscount();
        
        expect(result.current.discount).toBeNull();
        expect(result.current.error).toBeNull();
      });
    });
  });

  it('should handle empty discount code', async () => {
    const { result } = renderHook(() => useDiscountCode());

    await act(async () => {
      await result.current.applyDiscountCode('');
    });

    expect(result.current.error).toBe('Please enter a discount code');
    expect(firebaseService.getDiscountCode).not.toHaveBeenCalled();
  });

  it('should handle whitespace-only discount code', async () => {
    const { result } = renderHook(() => useDiscountCode());

    await act(async () => {
      await result.current.applyDiscountCode('   ');
    });

    expect(result.current.error).toBe('Please enter a discount code');
    expect(firebaseService.getDiscountCode).not.toHaveBeenCalled();
  });

  it('should normalize discount code case', async () => {
    const mockDiscount = {
      percentage: 25,
      active: true,
    };

    vi.mocked(firebaseService.getDiscountCode).mockResolvedValue(mockDiscount);

    const { result } = renderHook(() => useDiscountCode());

    await act(async () => {
      await result.current.applyDiscountCode('save25');
    });

    expect(firebaseService.getDiscountCode).toHaveBeenCalledWith('SAVE25');
  });

  it('should clear error when applying new discount code', async () => {
    const { result } = renderHook(() => useDiscountCode());

    // First, create an error
    vi.mocked(firebaseService.getDiscountCode).mockResolvedValue(null);
    await act(async () => {
      await result.current.applyDiscountCode('INVALID');
    });
    
    expect(result.current.error).toBe('Invalid discount code');

    // Then apply a valid code
    vi.mocked(firebaseService.getDiscountCode).mockResolvedValue({
      percentage: 10,
      active: true,
    });

    await act(async () => {
      await result.current.applyDiscountCode('VALID');
    });

    expect(result.current.error).toBeNull();
    expect(result.current.discount).toEqual({ percentage: 10, active: true });
  });
});
