import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import QuotePage from '../../pages/QuotePage';

// Mock the components and hooks
vi.mock('../../components/FormInput', () => ({
  default: ({ label, name, value, onChange, error, ...props }: any) => (
    <div>
      <label htmlFor={name}>{label}</label>
      <input
        id={name}
        name={name}
        value={value}
        onChange={onChange}
        data-testid={name}
        {...props}
      />
      {error && <span data-testid={`${name}-error`}>{error}</span>}
    </div>
  ),
}));

vi.mock('../../components/FormSelect', () => ({
  default: ({ label, name, value, onChange, error, children, ...props }: any) => (
    <div>
      <label htmlFor={name}>{label}</label>
      <select
        id={name}
        name={name}
        value={value}
        onChange={onChange}
        data-testid={name}
        {...props}
      >
        {children}
      </select>
      {error && <span data-testid={`${name}-error`}>{error}</span>}
    </div>
  ),
}));

vi.mock('../../components/FormTextarea', () => ({
  default: ({ label, name, value, onChange, ...props }: any) => (
    <div>
      <label htmlFor={name}>{label}</label>
      <textarea
        id={name}
        name={name}
        value={value}
        onChange={onChange}
        data-testid={name}
        {...props}
      />
    </div>
  ),
}));

vi.mock('../../components/DiscountCodeInput', () => ({
  default: ({ onApply, appliedDiscount, onClear }: any) => (
    <div data-testid="discount-code-input">
      <input
        data-testid="discount-code"
        placeholder="Enter discount code"
        onKeyDown={(e: any) => {
          if (e.key === 'Enter') {
            onApply('TEST10');
          }
        }}
      />
      {appliedDiscount && (
        <div data-testid="applied-discount">
          Discount: {appliedDiscount.percentage}%
          <button onClick={onClear} data-testid="clear-discount">Clear</button>
        </div>
      )}
    </div>
  ),
}));

vi.mock('../../hooks/useDiscountCode', () => ({
  useDiscountCode: () => ({
    discount: null,
    isLoading: false,
    error: null,
    applyDiscountCode: vi.fn(),
    clearDiscount: vi.fn(),
  }),
}));

vi.mock('../../hooks/useToast', () => ({
  useToast: () => ({
    toasts: [],
    showError: vi.fn(),
    showSuccess: vi.fn(),
    removeToast: vi.fn(),
  }),
}));

vi.mock('../../services/firebase', () => ({
  firebaseService: {
    addQuoteRequest: vi.fn().mockResolvedValue({ id: 'test-id' }),
  },
}));

vi.mock('../../services/analytics', () => ({
  analyticsService: {
    trackEvent: vi.fn(),
  },
}));

describe('QuotePage Component', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Form Rendering', () => {
    it('should render all form fields', () => {
      render(<QuotePage />);

      expect(screen.getByLabelText('Full Name')).toBeInTheDocument();
      expect(screen.getByLabelText('Email Address')).toBeInTheDocument();
      expect(screen.getByLabelText('Phone Number')).toBeInTheDocument();
      expect(screen.getByLabelText('Service Type')).toBeInTheDocument();
      expect(screen.getByLabelText('Cleaning Frequency')).toBeInTheDocument();
      expect(screen.getByLabelText('Additional Notes (Optional)')).toBeInTheDocument();
    });

    it('should render service type options', () => {
      render(<QuotePage />);

      const serviceSelect = screen.getByTestId('serviceType');
      expect(serviceSelect).toBeInTheDocument();
      
      // Check if the select has the expected options
      expect(screen.getByText('House Cleaning')).toBeInTheDocument();
      expect(screen.getByText('Office Cleaning')).toBeInTheDocument();
      expect(screen.getByText('Hotel Cleaning')).toBeInTheDocument();
      expect(screen.getByText('Hourly Cleaning (Customer Supplies)')).toBeInTheDocument();
      expect(screen.getByText('Hourly Cleaning (Company Supplies)')).toBeInTheDocument();
      expect(screen.getByText('Backyard Cleaning (Hourly)')).toBeInTheDocument();
    });

    it('should render frequency options', () => {
      render(<QuotePage />);

      const frequencySelect = screen.getByTestId('frequency');
      expect(frequencySelect).toBeInTheDocument();
      
      expect(screen.getByText('One-time cleaning')).toBeInTheDocument();
      expect(screen.getByText('Weekly')).toBeInTheDocument();
      expect(screen.getByText('Bi-weekly')).toBeInTheDocument();
      expect(screen.getByText('Monthly')).toBeInTheDocument();
    });

    it('should render additional services checkboxes', () => {
      render(<QuotePage />);

      expect(screen.getByText('Deep cleaning (+$25)')).toBeInTheDocument();
      expect(screen.getByText('Carpet cleaning (+$25)')).toBeInTheDocument();
      expect(screen.getByText('Window cleaning (+$25)')).toBeInTheDocument();
      expect(screen.getByText('Appliance cleaning (+$25)')).toBeInTheDocument();
      expect(screen.getByText('Garage cleaning (+$25)')).toBeInTheDocument();
      expect(screen.getByText('Basement cleaning (+$25)')).toBeInTheDocument();
    });
  });

  describe('Fixed Pricing Services', () => {
    it('should show property size field for fixed services', async () => {
      render(<QuotePage />);

      const serviceSelect = screen.getByTestId('serviceType');
      await user.selectOptions(serviceSelect, 'house');

      expect(screen.getByLabelText('Property Size')).toBeInTheDocument();
    });

    it('should not show hours field for fixed services', async () => {
      render(<QuotePage />);

      const serviceSelect = screen.getByTestId('serviceType');
      await user.selectOptions(serviceSelect, 'house');

      expect(screen.queryByLabelText('Number of Hours')).not.toBeInTheDocument();
    });

    it('should calculate pricing for fixed services', async () => {
      render(<QuotePage />);

      // Fill out the form
      await user.type(screen.getByTestId('name'), 'John Doe');
      await user.type(screen.getByTestId('email'), '<EMAIL>');
      await user.type(screen.getByTestId('phone'), '************');
      await user.selectOptions(screen.getByTestId('serviceType'), 'house');
      await user.selectOptions(screen.getByTestId('propertySize'), 'small');
      await user.selectOptions(screen.getByTestId('frequency'), 'one-time');

      // Wait for pricing to calculate
      await waitFor(() => {
        expect(screen.getByText('Instant Estimate')).toBeInTheDocument();
      });

      expect(screen.getAllByText('$150')).toHaveLength(2); // Should appear in subtotal and total
    });
  });

  describe('Hourly Pricing Services', () => {
    it('should show hours field for hourly services', async () => {
      render(<QuotePage />);

      const serviceSelect = screen.getByTestId('serviceType');
      await user.selectOptions(serviceSelect, 'hourly-customer-supply');

      expect(screen.getByLabelText('Number of Hours')).toBeInTheDocument();
    });

    it('should not show property size field for hourly services', async () => {
      render(<QuotePage />);

      const serviceSelect = screen.getByTestId('serviceType');
      await user.selectOptions(serviceSelect, 'hourly-customer-supply');

      expect(screen.queryByLabelText('Property Size')).not.toBeInTheDocument();
    });

    it('should show service information for hourly services', async () => {
      render(<QuotePage />);

      const serviceSelect = screen.getByTestId('serviceType');
      await user.selectOptions(serviceSelect, 'hourly-customer-supply');

      expect(screen.getByText('Hourly Service Information')).toBeInTheDocument();
      expect(screen.getByText('• Minimum booking: 2 hours')).toBeInTheDocument();
      expect(screen.getByText('• Rate: $25/hour')).toBeInTheDocument();
      expect(screen.getByText('• You provide cleaning supplies and equipment')).toBeInTheDocument();
    });

    it('should show hours options starting from minimum', async () => {
      render(<QuotePage />);

      const serviceSelect = screen.getByTestId('serviceType');
      await user.selectOptions(serviceSelect, 'hourly-customer-supply');

      expect(screen.getByTestId('selectedHours')).toBeInTheDocument();
      expect(screen.getByText('2 hours ($50 base cost)')).toBeInTheDocument();
      expect(screen.getByText('3 hours ($75 base cost)')).toBeInTheDocument();
      expect(screen.getByText('4 hours ($100 base cost)')).toBeInTheDocument();
    });

    it('should calculate pricing for hourly services', async () => {
      render(<QuotePage />);

      // Fill out the form
      await user.type(screen.getByTestId('name'), 'John Doe');
      await user.type(screen.getByTestId('email'), '<EMAIL>');
      await user.type(screen.getByTestId('phone'), '************');
      await user.selectOptions(screen.getByTestId('serviceType'), 'hourly-customer-supply');
      await user.selectOptions(screen.getByTestId('selectedHours'), '3');
      await user.selectOptions(screen.getByTestId('frequency'), 'one-time');

      // Wait for pricing to calculate
      await waitFor(() => {
        expect(screen.getByText('Instant Estimate')).toBeInTheDocument();
      });

      expect(screen.getByText('$25/hr')).toBeInTheDocument(); // Hourly rate
      expect(screen.getByText('3 hrs')).toBeInTheDocument(); // Selected hours
      expect(screen.getAllByText('$75')).toHaveLength(1); // Total
    });

    it('should show different pricing for different hourly services', async () => {
      render(<QuotePage />);

      // Test company supply service
      await user.selectOptions(screen.getByTestId('serviceType'), 'hourly-company-supply');
      
      expect(screen.getByText('• Rate: $35/hour')).toBeInTheDocument();
      expect(screen.getByText('• We provide all professional-grade supplies and equipment')).toBeInTheDocument();
    });

    it('should show reduced additional service pricing for hourly', async () => {
      render(<QuotePage />);

      await user.selectOptions(screen.getByTestId('serviceType'), 'hourly-customer-supply');

      expect(screen.getByText('Deep cleaning (+$15/service)')).toBeInTheDocument();
      expect(screen.getByText('Carpet cleaning (+$15/service)')).toBeInTheDocument();
    });
  });

  describe('Form Validation', () => {
    it('should show validation errors for empty required fields', async () => {
      render(<QuotePage />);

      const submitButton = screen.getByText('Get My Quote');
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByTestId('name-error')).toHaveTextContent('Full name is required');
        expect(screen.getByTestId('email-error')).toHaveTextContent('Email address is required');
        expect(screen.getByTestId('phone-error')).toHaveTextContent('Phone number is required');
        expect(screen.getByTestId('serviceType-error')).toHaveTextContent('Please select a service type');
        expect(screen.getByTestId('frequency-error')).toHaveTextContent('Please select cleaning frequency');
      });
    });

    it('should validate email format', async () => {
      render(<QuotePage />);

      await user.type(screen.getByTestId('email'), 'invalid-email');
      const submitButton = screen.getByText('Get My Quote');
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByTestId('email-error')).toHaveTextContent('Please enter a valid email address');
      });
    });

    it('should require property size for fixed services', async () => {
      render(<QuotePage />);

      await user.type(screen.getByTestId('name'), 'John Doe');
      await user.type(screen.getByTestId('email'), '<EMAIL>');
      await user.type(screen.getByTestId('phone'), '************');
      await user.selectOptions(screen.getByTestId('serviceType'), 'house');
      await user.selectOptions(screen.getByTestId('frequency'), 'weekly');

      // Don't select property size
      const submitButton = screen.getByText('Get My Quote');
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByTestId('propertySize-error')).toHaveTextContent('Please select property size');
      });
    });

    it('should require hours for hourly services', async () => {
      render(<QuotePage />);

      await user.type(screen.getByTestId('name'), 'John Doe');
      await user.type(screen.getByTestId('email'), '<EMAIL>');
      await user.type(screen.getByTestId('phone'), '************');
      await user.selectOptions(screen.getByTestId('serviceType'), 'hourly-customer-supply');
      await user.selectOptions(screen.getByTestId('frequency'), 'weekly');

      // Don't select hours
      const submitButton = screen.getByText('Get My Quote');
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByTestId('selectedHours-error')).toHaveTextContent('Please select number of hours');
      });
    });
  });

  describe('Additional Services', () => {
    it('should handle additional services selection', async () => {
      render(<QuotePage />);

      const deepCleaningCheckbox = screen.getByRole('checkbox', { name: /Deep cleaning/ });
      const carpetCleaningCheckbox = screen.getByRole('checkbox', { name: /Carpet cleaning/ });

      await user.click(deepCleaningCheckbox);
      await user.click(carpetCleaningCheckbox);

      expect(deepCleaningCheckbox).toBeChecked();
      expect(carpetCleaningCheckbox).toBeChecked();
    });

    it('should add additional services to pricing', async () => {
      render(<QuotePage />);

      // Fill out basic form
      await user.type(screen.getByTestId('name'), 'John Doe');
      await user.type(screen.getByTestId('email'), '<EMAIL>');
      await user.type(screen.getByTestId('phone'), '************');
      await user.selectOptions(screen.getByTestId('serviceType'), 'house');
      await user.selectOptions(screen.getByTestId('propertySize'), 'small');
      await user.selectOptions(screen.getByTestId('frequency'), 'one-time');

      // Add additional services
      const deepCleaningCheckbox = screen.getByRole('checkbox', { name: /Deep cleaning/ });
      await user.click(deepCleaningCheckbox);

      // Wait for pricing to update
      await waitFor(() => {
        const totalElement = screen.getByText('Total:').parentElement?.querySelector('.text-green-600');
        expect(totalElement).toHaveTextContent('$175'); // 150 + 25
      });
    });
  });

  describe('Successful Submission', () => {
    it('should show success page after form submission', async () => {
      render(<QuotePage />);

      // Fill out complete form
      await user.type(screen.getByTestId('name'), 'John Doe');
      await user.type(screen.getByTestId('email'), '<EMAIL>');
      await user.type(screen.getByTestId('phone'), '************');
      await user.selectOptions(screen.getByTestId('serviceType'), 'house');
      await user.selectOptions(screen.getByTestId('propertySize'), 'small');
      await user.selectOptions(screen.getByTestId('frequency'), 'one-time');

      const submitButton = screen.getByText('Get My Quote');
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('Thank You!')).toBeInTheDocument();
        expect(screen.getByText('Your request has been submitted. We\'ll contact you within 24 hours.')).toBeInTheDocument();
      });
    });

    it('should allow getting another quote', async () => {
      render(<QuotePage />);

      // Fill out and submit form
      await user.type(screen.getByTestId('name'), 'John Doe');
      await user.type(screen.getByTestId('email'), '<EMAIL>');
      await user.type(screen.getByTestId('phone'), '************');
      await user.selectOptions(screen.getByTestId('serviceType'), 'house');
      await user.selectOptions(screen.getByTestId('propertySize'), 'small');
      await user.selectOptions(screen.getByTestId('frequency'), 'one-time');

      const submitButton = screen.getByText('Get My Quote');
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('Thank You!')).toBeInTheDocument();
      });

      // Click get another quote
      const anotherQuoteButton = screen.getByText('Get Another Quote');
      await user.click(anotherQuoteButton);

      // Should be back to form
      expect(screen.getByLabelText('Full Name')).toBeInTheDocument();
      expect(screen.getByTestId('name')).toHaveValue('');
    });
  });

  describe('Edge Cases', () => {
    it('should handle service type changes', async () => {
      render(<QuotePage />);

      // Start with fixed service
      await user.selectOptions(screen.getByTestId('serviceType'), 'house');
      expect(screen.getByLabelText('Property Size')).toBeInTheDocument();

      // Change to hourly service
      await user.selectOptions(screen.getByTestId('serviceType'), 'hourly-customer-supply');
      expect(screen.queryByLabelText('Property Size')).not.toBeInTheDocument();
      expect(screen.getByLabelText('Number of Hours')).toBeInTheDocument();
    });

    it('should clear pricing when required fields are empty', async () => {
      render(<QuotePage />);

      // Fill out form to show pricing
      await user.type(screen.getByTestId('name'), 'John Doe');
      await user.type(screen.getByTestId('email'), '<EMAIL>');
      await user.type(screen.getByTestId('phone'), '************');
      await user.selectOptions(screen.getByTestId('serviceType'), 'house');
      await user.selectOptions(screen.getByTestId('propertySize'), 'small');
      await user.selectOptions(screen.getByTestId('frequency'), 'one-time');

      await waitFor(() => {
        expect(screen.getByText('Instant Estimate')).toBeInTheDocument();
      });

      // Clear service type
      await user.selectOptions(screen.getByTestId('serviceType'), '');

      // Pricing should disappear
      expect(screen.queryByText('Instant Estimate')).not.toBeInTheDocument();
    });
  });
});
