#!/usr/bin/env node

/**
 * Image Optimization Script for Ottawa Shine Solutions
 * Converts PNG images to WebP format for better performance
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const PUBLIC_DIR = path.join(__dirname, '../public');
const IMAGES_DIR = path.join(PUBLIC_DIR, 'images');

// Check if ImageMagick or sharp-cli is available
function checkImageOptimizationTools() {
  try {
    execSync('which convert', { stdio: 'ignore' });
    return 'imagemagick';
  } catch {
    try {
      execSync('which cwebp', { stdio: 'ignore' });
      return 'cwebp';
    } catch {
      console.log('⚠️  No image optimization tools found.');
      console.log('Please install one of the following:');
      console.log('- ImageMagick: brew install imagemagick');
      console.log('- WebP tools: brew install webp');
      return null;
    }
  }
}

function optimizeWithCwebp(inputPath, outputPath) {
  try {
    // Use cwebp with high quality settings for logos
    execSync(`cwebp -q 90 -m 6 "${inputPath}" -o "${outputPath}"`, { stdio: 'inherit' });
    return true;
  } catch (error) {
    console.error(`Failed to convert ${inputPath}:`, error.message);
    return false;
  }
}

function optimizeWithImageMagick(inputPath, outputPath) {
  try {
    // Use ImageMagick with high quality settings
    execSync(`convert "${inputPath}" -quality 90 "${outputPath}"`, { stdio: 'inherit' });
    return true;
  } catch (error) {
    console.error(`Failed to convert ${inputPath}:`, error.message);
    return false;
  }
}

function getFileSizeKB(filePath) {
  try {
    const stats = fs.statSync(filePath);
    return (stats.size / 1024).toFixed(1);
  } catch {
    return '0';
  }
}

function main() {
  console.log('🖼️  Starting image optimization...');
  
  const tool = checkImageOptimizationTools();
  if (!tool) {
    process.exit(1);
  }
  
  console.log(`Using ${tool} for optimization`);
  
  // Ensure images directory exists
  if (!fs.existsSync(IMAGES_DIR)) {
    fs.mkdirSync(IMAGES_DIR, { recursive: true });
  }
  
  // List of images to optimize
  const imagesToOptimize = [
    { input: path.join(PUBLIC_DIR, 'logo.png'), output: path.join(PUBLIC_DIR, 'logo.webp') },
    { input: path.join(IMAGES_DIR, 'logo-32.png'), output: path.join(IMAGES_DIR, 'logo-32.webp') },
    { input: path.join(IMAGES_DIR, 'logo-40.png'), output: path.join(IMAGES_DIR, 'logo-40.webp') },
    { input: path.join(IMAGES_DIR, 'logo-64.png'), output: path.join(IMAGES_DIR, 'logo-64.webp') },
    { input: path.join(IMAGES_DIR, 'logo-128.png'), output: path.join(IMAGES_DIR, 'logo-128.webp') },
    { input: path.join(IMAGES_DIR, 'logo-200.png'), output: path.join(IMAGES_DIR, 'logo-200.webp') }
  ];
  
  let totalSavings = 0;
  let successCount = 0;
  
  for (const { input, output } of imagesToOptimize) {
    if (!fs.existsSync(input)) {
      console.log(`⚠️  Skipping ${path.basename(input)} - file not found`);
      continue;
    }
    
    const originalSize = getFileSizeKB(input);
    console.log(`\n📸 Processing ${path.basename(input)} (${originalSize}KB)...`);
    
    let success = false;
    if (tool === 'cwebp') {
      success = optimizeWithCwebp(input, output);
    } else if (tool === 'imagemagick') {
      success = optimizeWithImageMagick(input, output);
    }
    
    if (success && fs.existsSync(output)) {
      const newSize = getFileSizeKB(output);
      const savings = parseFloat(originalSize) - parseFloat(newSize);
      totalSavings += savings;
      successCount++;
      
      console.log(`✅ Created ${path.basename(output)} (${newSize}KB) - Saved ${savings.toFixed(1)}KB`);
    }
  }
  
  console.log(`\n🎉 Optimization complete!`);
  console.log(`📊 Processed ${successCount} images`);
  console.log(`💾 Total savings: ${totalSavings.toFixed(1)}KB`);
  
  if (successCount > 0) {
    console.log('\n📝 Next steps:');
    console.log('1. Update your components to use <picture> elements with WebP sources');
    console.log('2. Test the website to ensure images load correctly');
    console.log('3. Run a new PageSpeed Insights test to verify improvements');
  }
}

if (require.main === module) {
  main();
}

module.exports = { main };
