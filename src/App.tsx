import { Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { lazy } from 'react';
// Lazy load heavy UI components
const Navigation = lazy(() => import(/* webpackChunkName: "navigation" */ './components/Navigation'));
const Footer = lazy(() => import(/* webpackChunkName: "footer" */ './components/Footer'));
import LoadingSpinner from './components/LoadingSpinner';
import ProtectedRoute from './components/ProtectedRoute';

import { AuthProvider } from './contexts/AuthContext';

// Lazy load page components with preload hints
const HomePage = lazy(() => import('./pages/HomePage'));
const ServicesPage = lazy(() => import('./pages/ServicesPage'));
const QuotePage = lazy(() => import('./pages/QuotePage'));
const ContactPage = lazy(() => import('./pages/ContactPage'));

// Admin features - only load when needed
const AdminPage = lazy(() => import(/* webpackChunkName: "admin" */ './pages/AdminPage'));
const LoginPage = lazy(() => import(/* webpackChunkName: "auth" */ './pages/LoginPage'));

// Performance monitor - only load in development
const PerformanceMonitor = lazy(() => import(/* webpackChunkName: "dev-tools" */ './components/PerformanceMonitor'));

// Lazy load heavy components
const AnalyticsProvider = lazy(() => import(/* webpackChunkName: "analytics" */ './components/AnalyticsProvider'));
const ErrorBoundary = lazy(() => import(/* webpackChunkName: "error-handling" */ './components/ErrorBoundary'));

function App() {
  return (
    <AuthProvider>
      <Router>
        <AnalyticsProvider>
          <div className="min-h-screen bg-gray-50">
            <ErrorBoundary>
              <Suspense fallback={<LoadingSpinner size="large" text="Loading..." />}>
              <Routes>
                {/* Public routes with navigation and footer */}
                <Route
                  path="/"
                  element={
                    <>
                      <Suspense fallback={<div className="h-16 bg-white shadow-sm animate-pulse" />}>
                        <Navigation />
                      </Suspense>
                      <HomePage />
                      <Suspense fallback={<div className="h-32 bg-gray-900 animate-pulse" />}>
                        <Footer />
                      </Suspense>
                    </>
                  }
                />
                <Route
                  path="/services"
                  element={
                    <>
                      <Suspense fallback={<div className="h-16 bg-white shadow-sm animate-pulse" />}>
                        <Navigation />
                      </Suspense>
                      <ServicesPage />
                      <Suspense fallback={<div className="h-32 bg-gray-900 animate-pulse" />}>
                        <Footer />
                      </Suspense>
                    </>
                  }
                />
                <Route
                  path="/quote"
                  element={
                    <>
                      <Navigation />
                      <QuotePage />
                      <Footer />
                    </>
                  }
                />
                <Route
                  path="/contact"
                  element={
                    <>
                      <Navigation />
                      <ContactPage />
                      <Footer />
                    </>
                  }
                />
                
                {/* Auth routes - minimal layout */}
                <Route path="/login" element={<LoginPage />} />
                
                {/* Protected admin route */}
                <Route
                  path="/admin"
                  element={
                    <ProtectedRoute>
                      <AdminPage />
                    </ProtectedRoute>
                  }
                />
                
                {/* Catch all - redirect to home */}
                <Route path="*" element={<Navigate to="/" replace />} />
              </Routes>
            </Suspense>
          </ErrorBoundary>

          {/* Performance Monitor (dev only) */}
          <Suspense fallback={null}>
            <PerformanceMonitor />
          </Suspense>
        </div>
        </AnalyticsProvider>
      </Router>
    </AuthProvider>
  );
}

export default App;
