// Performance monitoring utilities for production
export const performanceUtils = {
  // Track component loading times (production-safe)
  trackComponentLoad: function(componentName: string) {
    if (typeof window !== 'undefined' && window.performance) {
      const mark = componentName + '-start';
      performance.mark(mark);

      return function() {
        const endMark = componentName + '-end';
        performance.mark(endMark);
        performance.measure(componentName, mark, endMark);

        // Only log in development
        if (process.env.NODE_ENV === 'development') {
          console.log('Component ' + componentName + ' loaded');
        }
      };
    }
    return function() {};
  },

  // Track lazy loading effectiveness (production-safe)
  trackLazyLoad: function(chunkName: string) {
    if (process.env.NODE_ENV === 'development') {
      console.log('Lazy loading chunk: ' + chunkName);
    }

    // Send to analytics in production
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'lazy_load', {
        chunk_name: chunkName,
        event_category: 'performance'
      });
    }
  },

  // Measure Core Web Vitals (production-safe)
  measureCoreWebVitals: function() {
    if (process.env.NODE_ENV === 'development') {
      console.log('Performance monitoring initialized');
    }

    // Initialize real Core Web Vitals tracking for production
    if (typeof window !== 'undefined') {
      this.initCoreWebVitals();
    }
  },

  // Bundle size tracking (production-safe)
  trackBundleSize: function() {
    if (process.env.NODE_ENV === 'development') {
      console.log('Bundle size tracking initialized');
    }
  },

  // Initialize Core Web Vitals tracking
  initCoreWebVitals: function() {
    if (typeof window === 'undefined' || !window.performance) return;

    // Track FCP (First Contentful Paint)
    if ('PerformanceObserver' in window) {
      try {
        new PerformanceObserver((entryList) => {
          const entries = entryList.getEntries();
          const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');
          if (fcpEntry && window.gtag) {
            window.gtag('event', 'web_vital', {
              name: 'FCP',
              value: Math.round(fcpEntry.startTime),
              event_category: 'performance'
            });
          }
        }).observe({ entryTypes: ['paint'] });

        // Track LCP (Largest Contentful Paint)
        new PerformanceObserver((entryList) => {
          const entries = entryList.getEntries();
          const lastEntry = entries[entries.length - 1];
          if (lastEntry && window.gtag) {
            window.gtag('event', 'web_vital', {
              name: 'LCP',
              value: Math.round(lastEntry.startTime),
              event_category: 'performance'
            });
          }
        }).observe({ entryTypes: ['largest-contentful-paint'] });
      } catch (e) {
        // Silently fail in production
        if (process.env.NODE_ENV === 'development') {
          console.warn('Performance Observer not supported:', e);
        }
      }
    }
  }
};
