import type { PropertySize, CleaningFrequency, PricingResult, PricingMultipliers } from '../types';
import { hourlyServices, getDynamicHourlyServices } from './services';
import { getGlobalPricingConfig } from '../hooks/usePricing';

// Static pricing configuration (fallback)
const staticPricingMultipliers: PricingMultipliers = {
  sizeMultipliers: {
    'small': 1,
    'medium': 1.5,
    'large': 2,
    'xlarge': 3
  },

  frequencyDiscounts: {
    'one-time': 1,
    'weekly': 0.85,
    'bi-weekly': 0.9,
    'monthly': 0.95
  },

  // Estimated hours based on property size for hourly services
  estimatedHours: {
    'small': 2,
    'medium': 3,
    'large': 4,
    'xlarge': 6
  }
};

export const pricingCalculator = {
  // Legacy static properties for backward compatibility
  sizeMultipliers: staticPricingMultipliers.sizeMultipliers,
  frequencyDiscounts: staticPricingMultipliers.frequencyDiscounts,
  estimatedHours: staticPricingMultipliers.estimatedHours,
  
  calculate: (
    serviceType: string, 
    propertySize: PropertySize, 
    frequency: CleaningFrequency, 
    additionalServices: string[] = [], 
    discountPercentage: number = 0,
    basePrice: number = 80,
    selectedHours?: number
  ): PricingResult => {
    // Check if this is an hourly service
    const isHourlyService = hourlyServices[serviceType as keyof typeof hourlyServices];
    
    if (isHourlyService) {
      // Calculate hourly pricing
      const hourlyRate = isHourlyService.rate;
      const hours = selectedHours || isHourlyService.minHours;
      
      // Apply frequency discount to hourly rate
      const frequencyDiscount = pricingCalculator.frequencyDiscounts[frequency] || 1;
      const discountedHourlyRate = hourlyRate * frequencyDiscount;
      
      // Additional services for hourly (reduced rate)
      const additionalServicesPrice = additionalServices.length * 15; // Lower rate for hourly
      
      const subtotal = Math.round((discountedHourlyRate * hours) + additionalServicesPrice);
      const discountAmount = Math.round((subtotal * discountPercentage) / 100);
      
      return {
        subtotal,
        discountAmount,
        total: subtotal - discountAmount,
        isHourly: true,
        hourlyRate: discountedHourlyRate,
        estimatedHours: hours
      };
    } else {
      // Calculate fixed pricing (existing logic)
      const sizeMultiplier = pricingCalculator.sizeMultipliers[propertySize] || 1;
      const frequencyDiscount = pricingCalculator.frequencyDiscounts[frequency] || 1;
      const additionalServicesPrice = additionalServices.length * 25;
      
      const subtotal = (basePrice * sizeMultiplier * frequencyDiscount) + additionalServicesPrice;
      const discountAmount = Math.round((subtotal * discountPercentage) / 100);
      
      return {
        subtotal: Math.round(subtotal),
        discountAmount: discountAmount,
        total: Math.round(subtotal) - discountAmount,
        isHourly: false
      };
    }
  },

  // Dynamic pricing calculation using Firebase configuration
  calculateDynamic: async (
    serviceType: string,
    propertySize: PropertySize,
    frequency: CleaningFrequency,
    additionalServices: string[] = [],
    discountPercentage: number = 0,
    selectedHours?: number
  ): Promise<PricingResult> => {
    try {
      const pricingConfig = await getGlobalPricingConfig();
      const hourlyServicesConfig = await getDynamicHourlyServices();

      // Find the service pricing
      const servicePricing = pricingConfig.services.find(s => s.id === serviceType);
      if (!servicePricing) {
        throw new Error(`Service pricing not found for ${serviceType}`);
      }

      // Check if this is an hourly service
      const isHourlyService = hourlyServicesConfig[serviceType as keyof typeof hourlyServicesConfig];

      if (isHourlyService && servicePricing.isHourly) {
        // Calculate hourly pricing using dynamic rates
        const hourlyRate = servicePricing.rate || isHourlyService.rate;
        const hours = selectedHours || servicePricing.minHours || isHourlyService.minHours;

        // Apply frequency discount to hourly rate
        const frequencyDiscount = pricingConfig.multipliers.frequencyDiscounts[frequency] || 1;
        const discountedHourlyRate = hourlyRate * frequencyDiscount;

        // Additional services for hourly (reduced rate)
        const additionalServicesPrice = additionalServices.length * 15;

        const subtotal = Math.round((discountedHourlyRate * hours) + additionalServicesPrice);
        const discountAmount = Math.round((subtotal * discountPercentage) / 100);

        return {
          subtotal,
          discountAmount,
          total: subtotal - discountAmount,
          isHourly: true,
          hourlyRate: discountedHourlyRate,
          estimatedHours: hours
        };
      } else {
        // Calculate fixed pricing using dynamic multipliers
        const sizeMultiplier = pricingConfig.multipliers.sizeMultipliers[propertySize] || 1;
        const frequencyDiscount = pricingConfig.multipliers.frequencyDiscounts[frequency] || 1;
        const additionalServicesPrice = additionalServices.length * 25;

        const subtotal = (servicePricing.basePrice * sizeMultiplier * frequencyDiscount) + additionalServicesPrice;
        const discountAmount = Math.round((subtotal * discountPercentage) / 100);

        return {
          subtotal: Math.round(subtotal),
          discountAmount: discountAmount,
          total: Math.round(subtotal) - discountAmount,
          isHourly: false
        };
      }
    } catch (error) {
      console.error('Error calculating dynamic pricing:', error);

      // Fallback to static pricing calculation
      return pricingCalculator.calculate(
        serviceType,
        propertySize,
        frequency,
        additionalServices,
        discountPercentage,
        80, // Default base price
        selectedHours
      );
    }
  }
};
