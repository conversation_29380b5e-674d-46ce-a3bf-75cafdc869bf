import type { LucideIcon } from 'lucide-react';

export interface Service {
  id: string;
  name: string;
  icon: LucideIcon;
  description: string;
  features: string[];
  basePrice: number;
  isHourly?: boolean;
  rate?: number; // For hourly services
}

export interface QuoteFormData {
  name: string;
  email: string;
  phone: string;
  serviceType: string;
  propertySize: string;
  frequency: string;
  additionalServices: string[];
  message: string;
  paymentMethod: 'fixed' | 'hourly';
  supplyMethod?: 'customer' | 'company';
  selectedHours?: number;
}

export interface ContactFormData {
  name: string;
  email: string;
  phone: string;
  subject: string;
  message: string;
}

export interface PricingResult {
  subtotal: number;
  discountAmount: number;
  total: number;
  isHourly?: boolean;
  hourlyRate?: number;
  estimatedHours?: number;
}

export interface DiscountCode {
  percentage: number;
  active: boolean;
  createdAt?: string;
  updatedAt?: string;
  expiresAt?: any; // Firebase Timestamp
}

export interface DiscountCodeWithId extends DiscountCode {
  id: string;
  code: string;
}

export interface AdminAnalytics {
  totalQuotes: number;
  totalContacts: number;
  pendingQuotes: number;
  newContacts: number;
  totalRevenue: number;
}

// Pricing Configuration Types
export interface ServicePricing {
  id: string;
  name: string;
  basePrice: number;
  isHourly: boolean;
  rate?: number; // For hourly services
  minHours?: number; // For hourly services
}

export interface PricingMultipliers {
  sizeMultipliers: {
    small: number;
    medium: number;
    large: number;
    xlarge: number;
  };
  frequencyDiscounts: {
    'one-time': number;
    weekly: number;
    'bi-weekly': number;
    monthly: number;
  };
  estimatedHours: {
    small: number;
    medium: number;
    large: number;
    xlarge: number;
  };
}

export interface PricingConfiguration {
  services: ServicePricing[];
  multipliers: PricingMultipliers;
  lastUpdated: string;
  updatedBy: string;
}

export interface PricingConfigurationWithId extends PricingConfiguration {
  id: string;
}

export type PropertySize = 'small' | 'medium' | 'large' | 'xlarge';
export type CleaningFrequency = 'one-time' | 'weekly' | 'bi-weekly' | 'monthly';
export type Page = 'home' | 'services' | 'quote' | 'contact' | 'admin' | 'login';

export interface QuoteSubmission extends QuoteFormData {
  pricing?: PricingResult;
  discountCode?: string | null;
  timestamp?: string;
  status?: string;
}

// Performance monitoring types
export interface PerformanceMetric {
  name: string;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  timestamp: number;
}

// Note: gtag types are declared in src/services/analytics.ts
