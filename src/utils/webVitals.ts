// Core Web Vitals monitoring for production
// Ultra-simplified version to avoid build issues

interface WebVitalMetric {
  name: string;
  value: number;
  rating: string;
}

interface WebVitalsConfig {
  reportToAnalytics?: boolean;
  reportToConsole?: boolean;
}

function reportMetric(metric: WebVitalMetric, config: WebVitalsConfig) {
  // Only log to console in development
  if (config.reportToConsole && process.env.NODE_ENV === 'development') {
    console.log(metric.name + ': ' + metric.value.toFixed(2) + 'ms (' + metric.rating + ')');
  }

  // Send to analytics in production
  if (config.reportToAnalytics && typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'web_vital', {
      name: metric.name,
      value: Math.round(metric.value),
      rating: metric.rating,
      event_category: 'performance'
    });
  }
}

export function initWebVitals(config: WebVitalsConfig = {}) {
  if (typeof window === 'undefined') {
    return;
  }

  const defaultConfig: WebVitalsConfig = {
    reportToAnalytics: true,
    reportToConsole: process.env.NODE_ENV === 'development',
    ...config
  };

  // Simple performance tracking
  setTimeout(function() {
    try {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigation) {
        const ttfb = navigation.responseStart - navigation.requestStart;
        const metric: WebVitalMetric = {
          name: 'TTFB',
          value: ttfb,
          rating: ttfb <= 800 ? 'good' : ttfb <= 1800 ? 'needs-improvement' : 'poor'
        };
        reportMetric(metric, defaultConfig);
      }
    } catch (e) {
      // Only warn in development
      if (process.env.NODE_ENV === 'development') {
        console.warn('Performance monitoring not supported:', e);
      }
    }
  }, 1000);
}

export function startWebVitalsMonitoring() {
  if (typeof window !== 'undefined') {
    if (document.readyState === 'complete') {
      initWebVitals();
    } else {
      window.addEventListener('load', function() {
        setTimeout(function() {
          initWebVitals();
        }, 1000);
      });
    }
  }
}

export default initWebVitals;
