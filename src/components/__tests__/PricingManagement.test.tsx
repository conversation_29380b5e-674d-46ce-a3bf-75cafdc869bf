import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import PricingManagement from '../PricingManagement';
import { AuthProvider } from '../../contexts/AuthContext';
import type { PricingConfiguration } from '../../types';

// Mock Firebase service
vi.mock('../../services/firebase', () => ({
  firebaseService: {
    admin: {
      getPricingConfiguration: vi.fn(),
      savePricingConfiguration: vi.fn()
    }
  }
}));

// Mock auth context
vi.mock('../../contexts/AuthContext', () => ({
  AuthProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  useAuth: () => ({
    user: { email: '<EMAIL>', displayName: 'Test Admin' }
  })
}));

const mockPricingConfig: PricingConfiguration = {
  services: [
    { id: 'house', name: 'House Cleaning', basePrice: 250, isHourly: false },
    { id: 'office', name: 'Office Cleaning', basePrice: 200, isHourly: false },
    { id: 'hourly-customer-supply', name: 'Hourly (Customer Supply)', basePrice: 25, isHourly: true, rate: 25, minHours: 2 }
  ],
  multipliers: {
    sizeMultipliers: { small: 1, medium: 1.5, large: 2, xlarge: 3 },
    frequencyDiscounts: { 'one-time': 1, weekly: 0.85, 'bi-weekly': 0.9, monthly: 0.95 },
    estimatedHours: { small: 2, medium: 3, large: 4, xlarge: 6 }
  },
  lastUpdated: '2024-01-01T00:00:00Z',
  updatedBy: '<EMAIL>'
};

describe('PricingManagement', () => {
  beforeEach(() => {
    const { firebaseService } = require('../../services/firebase');
    firebaseService.admin.getPricingConfiguration.mockResolvedValue(mockPricingConfig);
    firebaseService.admin.savePricingConfiguration.mockResolvedValue({ success: true });
  });

  it('should render pricing management interface', async () => {
    render(
      <AuthProvider>
        <PricingManagement />
      </AuthProvider>
    );

    await waitFor(() => {
      expect(screen.getByText('Pricing Management')).toBeInTheDocument();
    });

    expect(screen.getByText('Service Prices')).toBeInTheDocument();
    expect(screen.getByText('Pricing Multipliers')).toBeInTheDocument();
  });

  it('should display service pricing information', async () => {
    render(
      <AuthProvider>
        <PricingManagement />
      </AuthProvider>
    );

    await waitFor(() => {
      expect(screen.getByText('House Cleaning')).toBeInTheDocument();
    });

    expect(screen.getByText('Office Cleaning')).toBeInTheDocument();
    expect(screen.getByText('Hourly (Customer Supply)')).toBeInTheDocument();
  });

  it('should allow editing service prices', async () => {
    render(
      <AuthProvider>
        <PricingManagement />
      </AuthProvider>
    );

    await waitFor(() => {
      expect(screen.getByDisplayValue('150')).toBeInTheDocument();
    });

    const priceInput = screen.getByDisplayValue('150');
    fireEvent.change(priceInput, { target: { value: '175' } });

    expect(priceInput).toHaveValue('175');
  });

  it('should switch between tabs', async () => {
    render(
      <AuthProvider>
        <PricingManagement />
      </AuthProvider>
    );

    await waitFor(() => {
      expect(screen.getByText('Service Prices')).toBeInTheDocument();
    });

    const multipliersTab = screen.getByText('Pricing Multipliers');
    fireEvent.click(multipliersTab);

    expect(screen.getByText('Property Size Multipliers')).toBeInTheDocument();
    expect(screen.getByText('Frequency Discounts')).toBeInTheDocument();
  });

  it('should save pricing configuration', async () => {
    const { firebaseService } = require('../../services/firebase');
    
    render(
      <AuthProvider>
        <PricingManagement />
      </AuthProvider>
    );

    await waitFor(() => {
      expect(screen.getByText('Save Changes')).toBeInTheDocument();
    });

    const saveButton = screen.getByText('Save Changes');
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(firebaseService.admin.savePricingConfiguration).toHaveBeenCalled();
    });
  });

  it('should display last updated information', async () => {
    render(
      <AuthProvider>
        <PricingManagement />
      </AuthProvider>
    );

    await waitFor(() => {
      expect(screen.getByText(/Last updated:/)).toBeInTheDocument();
    });

    expect(screen.getByText(/Updated by: <EMAIL>/)).toBeInTheDocument();
  });

  it('should handle loading state', () => {
    const { firebaseService } = require('../../services/firebase');
    firebaseService.admin.getPricingConfiguration.mockImplementation(
      () => new Promise(resolve => setTimeout(resolve, 1000))
    );

    render(
      <AuthProvider>
        <PricingManagement />
      </AuthProvider>
    );

    // Should show loading spinner initially
    expect(screen.getByRole('status')).toBeInTheDocument();
  });

  it('should handle error state', async () => {
    const { firebaseService } = require('../../services/firebase');
    firebaseService.admin.getPricingConfiguration.mockRejectedValue(new Error('Firebase error'));

    render(
      <AuthProvider>
        <PricingManagement />
      </AuthProvider>
    );

    await waitFor(() => {
      expect(screen.getByText('Failed to Load Pricing Configuration')).toBeInTheDocument();
    });
  });

  it('should display hourly service fields', async () => {
    render(
      <AuthProvider>
        <PricingManagement />
      </AuthProvider>
    );

    await waitFor(() => {
      expect(screen.getByText('Hourly (Customer Supply)')).toBeInTheDocument();
    });

    // Should show hourly rate and minimum hours fields for hourly services
    expect(screen.getByDisplayValue('25')).toBeInTheDocument(); // hourly rate
    expect(screen.getByDisplayValue('2')).toBeInTheDocument(); // minimum hours
  });
});
