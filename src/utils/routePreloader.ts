// Route preloader utility for better performance
export const preloadRoute = (routeName: string) => {
  switch (routeName) {
    case 'services':
      return import('../pages/ServicesPage');
    case 'quote':
      return import('../pages/QuotePage');
    case 'contact':
      return import('../pages/ContactPage');
    case 'admin':
      return import('../pages/AdminPage');
    case 'login':
      return import('../pages/LoginPage');
    default:
      return Promise.resolve();
  }
};

// Preload routes on hover for instant navigation
export const setupRoutePreloading = () => {
  if (typeof window !== 'undefined') {
    const links = document.querySelectorAll('a[href^="/"]');
    
    links.forEach(link => {
      link.addEventListener('mouseenter', () => {
        const href = link.getAttribute('href');
        if (href) {
          const routeName = href.slice(1) || 'home';
          preloadRoute(routeName);
        }
      });
    });
  }
};

// Preload critical routes after initial load
export const preloadCriticalRoutes = () => {
  if (typeof window !== 'undefined') {
    // Preload services page (most likely next page)
    setTimeout(() => {
      preloadRoute('services');
    }, 2000);
    
    // Preload quote page (conversion page)
    setTimeout(() => {
      preloadRoute('quote');
    }, 4000);
  }
};
