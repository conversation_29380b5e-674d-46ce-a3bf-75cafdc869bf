import React, { useState, useEffect } from 'react';
import { Zap, Clock } from 'lucide-react';

interface PerformanceScore {
  score: number;
  fcp: number;
  lcp: number;
  rating: 'good' | 'needs-improvement' | 'poor';
}

const PerformanceBadge: React.FC = () => {
  const [performance, setPerformance] = useState<PerformanceScore | null>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Only show if explicitly enabled or in development
    const showBadge = process.env.NODE_ENV === 'development' || 
                     (typeof window !== 'undefined' && 
                      localStorage.getItem('show-performance-badge') === 'true');
    
    if (showBadge && typeof window !== 'undefined') {
      measurePerformance();
    }
  }, []);

  const measurePerformance = () => {
    if (!window.performance) return;

    setTimeout(() => {
      try {
        const navigation = window.performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        const paintEntries = window.performance.getEntriesByType('paint');

        const fcp = paintEntries.find((entry: any) => entry.name === 'first-contentful-paint')?.startTime || 0;
        const ttfb = navigation.responseStart - navigation.requestStart;
        
        // Simple performance score calculation
        let score = 100;
        if (fcp > 1800) score -= 20;
        else if (fcp > 1000) score -= 10;
        
        if (ttfb > 800) score -= 15;
        else if (ttfb > 400) score -= 5;
        
        const rating: 'good' | 'needs-improvement' | 'poor' = 
          score >= 90 ? 'good' : score >= 70 ? 'needs-improvement' : 'poor';

        setPerformance({
          score: Math.max(0, score),
          fcp,
          lcp: 0, // Will be updated if available
          rating
        });
        
        setIsVisible(true);
      } catch (e) {
        // Silently fail in production
        if (process.env.NODE_ENV === 'development') {
          console.warn('Performance measurement failed:', e);
        }
      }
    }, 2000);
  };

  if (!isVisible || !performance) {
    return null;
  }

  const getScoreColor = (rating: string) => {
    switch (rating) {
      case 'good': return 'text-green-600 bg-green-50 border-green-200';
      case 'needs-improvement': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'poor': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getScoreIcon = (rating: string) => {
    switch (rating) {
      case 'good': return <Zap className="h-4 w-4" />;
      case 'needs-improvement': return <Clock className="h-4 w-4" />;
      case 'poor': return <Clock className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  return (
    <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getScoreColor(performance.rating)}`}>
      {getScoreIcon(performance.rating)}
      <span className="ml-1">
        Performance: {performance.score}/100
      </span>
      {process.env.NODE_ENV === 'development' && (
        <span className="ml-2 text-xs opacity-75">
          FCP: {Math.round(performance.fcp)}ms
        </span>
      )}
    </div>
  );
};

export default PerformanceBadge;
