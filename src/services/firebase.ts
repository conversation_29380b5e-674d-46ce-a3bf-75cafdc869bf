import { initializeApp } from 'firebase/app';
import { getFirestore, collection, addDoc, doc, getDoc, getDocs, updateDoc, deleteDoc, setDoc, query, orderBy, where, serverTimestamp } from 'firebase/firestore';
import { getAuth, GoogleAuthProvider, signInWithPopup, signOut, onAuthStateChanged } from 'firebase/auth';
import type { QuoteSubmission, ContactFormData, DiscountCode, PricingConfiguration, ServicePricing, PricingMultipliers } from '../types';

// Firebase configuration from environment variables
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID,
  measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID
};

// Validate Firebase configuration
if (!firebaseConfig.apiKey || !firebaseConfig.projectId) {
  console.error('Firebase configuration is missing. Please check your .env file.');
  console.error('Current config:', {
    apiKey: firebaseConfig.apiKey ? 'Set' : 'Missing',
    authDomain: firebaseConfig.authDomain ? 'Set' : 'Missing',
    projectId: firebaseConfig.projectId ? 'Set' : 'Missing',
    storageBucket: firebaseConfig.storageBucket ? 'Set' : 'Missing',
    messagingSenderId: firebaseConfig.messagingSenderId ? 'Set' : 'Missing',
    appId: firebaseConfig.appId ? 'Set' : 'Missing',
  });
}

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);
const auth = getAuth(app);
const googleProvider = new GoogleAuthProvider();

export const firebaseService = {
  // Add quote request to Firestore
  addQuoteRequest: async (quoteData: QuoteSubmission) => {
    try {
      // Ensure required fields are present and validate data (message is optional)
      const requiredFields = ['name', 'email', 'phone', 'serviceType', 'propertySize', 'frequency'];
      for (const field of requiredFields) {
        if (!quoteData[field as keyof QuoteSubmission]) {
          throw new Error(`Missing required field: ${field}`);
        }
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(quoteData.email)) {
        throw new Error('Invalid email format');
      }

      // Validate property size and frequency values
      const validPropertySizes = ['small', 'medium', 'large', 'xlarge'];
      const validFrequencies = ['one-time', 'weekly', 'bi-weekly', 'monthly'];
      
      if (!validPropertySizes.includes(quoteData.propertySize)) {
        throw new Error('Invalid property size');
      }
      
      if (!validFrequencies.includes(quoteData.frequency)) {
        throw new Error('Invalid frequency');
      }

      const docRef = await addDoc(collection(db, 'quotes'), {
        ...quoteData,
        timestamp: serverTimestamp(),
        status: 'pending'
      });
      return { id: docRef.id, success: true };
    } catch (error) {
      console.error('Error adding quote request:', error);
      throw error instanceof Error ? error : new Error('Failed to submit quote request');
    }
  },

  // Add contact form submission to Firestore
  addContactMessage: async (contactData: ContactFormData) => {
    try {
      // Ensure required fields are present and validate data
      const requiredFields = ['name', 'email', 'phone', 'subject', 'message'];
      for (const field of requiredFields) {
        if (!contactData[field as keyof ContactFormData]) {
          throw new Error(`Missing required field: ${field}`);
        }
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(contactData.email)) {
        throw new Error('Invalid email format');
      }

      const docRef = await addDoc(collection(db, 'contacts'), {
        ...contactData,
        timestamp: serverTimestamp(),
        status: 'new'
      });
      return { id: docRef.id, success: true };
    } catch (error) {
      console.error('Error adding contact message:', error);
      throw error instanceof Error ? error : new Error('Failed to submit contact message');
    }
  },

  // Get discount code from Firestore
  getDiscountCode: async (code: string): Promise<DiscountCode | null> => {
    try {
      const docRef = doc(db, 'discountCodes', code.toUpperCase());
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        const data = docSnap.data() as DiscountCode;
        return data;
      } else {
        return null;
      }
    } catch (error) {
      console.error('Error fetching discount code:', error);
      return null;
    }
  },

  // Authentication functions
  auth: {
    // Sign in with Google
    signInWithGoogle: async () => {
      try {
        const result = await signInWithPopup(auth, googleProvider);
        return { user: result.user, success: true };
      } catch (error: any) {
        console.error('Error signing in with Google:', error);
        
        // Provide more specific error messages
        let errorMessage = 'Failed to sign in with Google';
        
        if (error.code === 'auth/configuration-not-found') {
          errorMessage = 'Firebase project is not properly configured. Please check your Firebase settings.';
        } else if (error.code === 'auth/popup-closed-by-user') {
          errorMessage = 'Sign-in was cancelled by user.';
        } else if (error.code === 'auth/popup-blocked') {
          errorMessage = 'Sign-in popup was blocked by your browser.';
        } else if (error.code === 'auth/network-request-failed') {
          errorMessage = 'Network error. Please check your internet connection.';
        }
        
        throw new Error(errorMessage);
      }
    },

    // Sign out
    signOut: async () => {
      try {
        await signOut(auth);
        return { success: true };
      } catch (error) {
        console.error('Error signing out:', error);
        throw new Error('Failed to sign out');
      }
    },

    // Check if user is admin (you can customize this logic)
    isAdmin: (user: any) => {
      // Add your admin email addresses here
      const adminEmails = [
        '<EMAIL>', // Your actual admin email
      ];
      return user && adminEmails.includes(user.email);
    },

    // Auth state observer
    onAuthStateChanged: (callback: (user: any) => void) => {
      return onAuthStateChanged(auth, callback);
    },

    // Get current user
    getCurrentUser: () => auth.currentUser
  },

  // Admin functions
  admin: {
    // Get all quotes
    getQuotes: async () => {
      try {
        const q = query(collection(db, 'quotes'), orderBy('timestamp', 'desc'));
        const querySnapshot = await getDocs(q);
        return querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        } as any));
      } catch (error) {
        console.error('Error fetching quotes:', error);
        throw new Error('Failed to fetch quotes');
      }
    },

    // Get all contacts
    getContacts: async () => {
      try {
        const q = query(collection(db, 'contacts'), orderBy('timestamp', 'desc'));
        const querySnapshot = await getDocs(q);
        return querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        } as any));
      } catch (error) {
        console.error('Error fetching contacts:', error);
        throw new Error('Failed to fetch contacts');
      }
    },

    // Update quote status
    updateQuoteStatus: async (quoteId: string, status: string) => {
      try {
        const quoteRef = doc(db, 'quotes', quoteId);
        await updateDoc(quoteRef, { 
          status,
          updatedAt: serverTimestamp()
        });
        return { success: true };
      } catch (error) {
        console.error('Error updating quote status:', error);
        throw new Error('Failed to update quote status');
      }
    },

    // Update contact status
    updateContactStatus: async (contactId: string, status: string) => {
      try {
        const contactRef = doc(db, 'contacts', contactId);
        await updateDoc(contactRef, { 
          status,
          updatedAt: serverTimestamp()
        });
        return { success: true };
      } catch (error) {
        console.error('Error updating contact status:', error);
        throw new Error('Failed to update contact status');
      }
    },

    // Delete quote
    deleteQuote: async (quoteId: string) => {
      try {
        await deleteDoc(doc(db, 'quotes', quoteId));
        return { success: true };
      } catch (error) {
        console.error('Error deleting quote:', error);
        throw new Error('Failed to delete quote');
      }
    },

    // Delete contact
    deleteContact: async (contactId: string) => {
      try {
        await deleteDoc(doc(db, 'contacts', contactId));
        return { success: true };
      } catch (error) {
        console.error('Error deleting contact:', error);
        throw new Error('Failed to delete contact');
      }
    },

    // Get analytics data
    getAnalytics: async () => {
      try {
        const quotesSnapshot = await getDocs(collection(db, 'quotes'));
        const contactsSnapshot = await getDocs(collection(db, 'contacts'));
        
        const quotes = quotesSnapshot.docs.map(doc => doc.data());
        const contacts = contactsSnapshot.docs.map(doc => doc.data());

        return {
          totalQuotes: quotes.length,
          totalContacts: contacts.length,
          pendingQuotes: quotes.filter(q => q.status === 'pending').length,
          newContacts: contacts.filter(c => c.status === 'new').length,
          totalRevenue: quotes
            .filter(q => q.status === 'completed')
            .reduce((sum, q) => sum + (q.pricing?.total || 0), 0)
        };
      } catch (error) {
        console.error('Error fetching analytics:', error);
        throw new Error('Failed to fetch analytics');
      }
    },

    // Discount code management
    createDiscountCode: async (code: string, percentage: number, active: boolean = true) => {
      try {
        const codeRef = doc(db, 'discountCodes', code.toUpperCase());
        await setDoc(codeRef, {
          percentage,
          active,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        });
        return { success: true };
      } catch (error) {
        console.error('Error creating discount code:', error);
        throw new Error('Failed to create discount code');
      }
    },

    // Get all discount codes
    getDiscountCodes: async () => {
      try {
        const querySnapshot = await getDocs(collection(db, 'discountCodes'));
        return querySnapshot.docs.map(doc => ({
          id: doc.id,
          code: doc.id,
          ...doc.data()
        }));
      } catch (error) {
        console.error('Error fetching discount codes:', error);
        throw new Error('Failed to fetch discount codes');
      }
    },

    // Update discount code
    updateDiscountCode: async (code: string, data: Partial<DiscountCode>) => {
      try {
        const codeRef = doc(db, 'discountCodes', code.toUpperCase());
        await updateDoc(codeRef, {
          ...data,
          updatedAt: serverTimestamp()
        });
        return { success: true };
      } catch (error) {
        console.error('Error updating discount code:', error);
        throw new Error('Failed to update discount code');
      }
    },

    // Delete discount code
    deleteDiscountCode: async (code: string) => {
      try {
        await deleteDoc(doc(db, 'discountCodes', code.toUpperCase()));
        return { success: true };
      } catch (error) {
        console.error('Error deleting discount code:', error);
        throw new Error('Failed to delete discount code');
      }
    },

    // Pricing Configuration Management
    // Get pricing configuration
    getPricingConfiguration: async () => {
      try {
        const docRef = doc(db, 'settings', 'pricing');
        const docSnap = await getDoc(docRef);

        if (docSnap.exists()) {
          return { id: docSnap.id, ...docSnap.data() } as PricingConfiguration & { id: string };
        } else {
          // Return null if no configuration exists - will be handled by the calling code
          return null;
        }
      } catch (error) {
        console.error('Error fetching pricing configuration:', error);

        // Check if it's a permissions error
        if (error instanceof Error && error.message.includes('permissions')) {
          console.warn('Insufficient permissions to access pricing configuration. Make sure Firestore rules allow admin access to settings collection.');
        }

        throw new Error('Failed to fetch pricing configuration');
      }
    },

    // Initialize default pricing configuration
    initializeDefaultPricing: async (updatedBy: string) => {
      try {
        const defaultConfig: Omit<PricingConfiguration, 'lastUpdated' | 'updatedBy'> = {
          services: [
            { id: 'house', name: 'House Cleaning', basePrice: 250, isHourly: false },
            { id: 'office', name: 'Office Cleaning', basePrice: 200, isHourly: false },
            { id: 'hotel', name: 'Hotel Cleaning', basePrice: 300, isHourly: false },
            { id: 'hourly-customer-supply', name: 'Hourly (Customer Supply)', basePrice: 25, isHourly: true, rate: 25, minHours: 2 },
            { id: 'hourly-company-supply', name: 'Hourly (Company Supply)', basePrice: 35, isHourly: true, rate: 35, minHours: 2 },
            { id: 'backyard-hourly', name: 'Backyard Cleaning (Hourly)', basePrice: 30, isHourly: true, rate: 30, minHours: 2 }
          ],
          multipliers: {
            sizeMultipliers: { small: 1, medium: 1.5, large: 2, xlarge: 3 },
            frequencyDiscounts: { 'one-time': 1, weekly: 0.85, 'bi-weekly': 0.9, monthly: 0.95 },
            estimatedHours: { small: 2, medium: 3, large: 4, xlarge: 6 }
          }
        };

        const result = await firebaseService.admin.savePricingConfiguration(defaultConfig, updatedBy);
        console.log('Default pricing configuration initialized successfully');
        return result;
      } catch (error) {
        console.error('Error initializing default pricing configuration:', error);
        throw new Error('Failed to initialize default pricing configuration');
      }
    },

    // Save pricing configuration
    savePricingConfiguration: async (pricingConfig: Omit<PricingConfiguration, 'lastUpdated' | 'updatedBy'>, updatedBy: string) => {
      try {
        const configWithMetadata: PricingConfiguration = {
          ...pricingConfig,
          lastUpdated: new Date().toISOString(),
          updatedBy
        };

        await setDoc(doc(db, 'settings', 'pricing'), configWithMetadata);
        return { success: true };
      } catch (error) {
        console.error('Error saving pricing configuration:', error);
        throw new Error('Failed to save pricing configuration');
      }
    },

    // Update specific service pricing
    updateServicePricing: async (serviceId: string, pricing: Partial<ServicePricing>, updatedBy: string) => {
      try {
        const currentConfig = await firebaseService.admin.getPricingConfiguration();
        if (!currentConfig) {
          throw new Error('No pricing configuration found');
        }

        const updatedServices = currentConfig.services.map(service =>
          service.id === serviceId ? { ...service, ...pricing } : service
        );

        const updatedConfig: PricingConfiguration = {
          ...currentConfig,
          services: updatedServices,
          lastUpdated: new Date().toISOString(),
          updatedBy
        };

        await setDoc(doc(db, 'settings', 'pricing'), updatedConfig);
        return { success: true };
      } catch (error) {
        console.error('Error updating service pricing:', error);
        throw new Error('Failed to update service pricing');
      }
    },

    // Update pricing multipliers
    updatePricingMultipliers: async (multipliers: Partial<PricingMultipliers>, updatedBy: string) => {
      try {
        const currentConfig = await firebaseService.admin.getPricingConfiguration();
        if (!currentConfig) {
          throw new Error('No pricing configuration found');
        }

        const updatedConfig: PricingConfiguration = {
          ...currentConfig,
          multipliers: { ...currentConfig.multipliers, ...multipliers },
          lastUpdated: new Date().toISOString(),
          updatedBy
        };

        await setDoc(doc(db, 'settings', 'pricing'), updatedConfig);
        return { success: true };
      } catch (error) {
        console.error('Error updating pricing multipliers:', error);
        throw new Error('Failed to update pricing multipliers');
      }
    }
  },

  // Generic Firestore methods for analytics
  addDocument: async (collectionName: string, data: any) => {
    try {
      const docRef = await addDoc(collection(db, collectionName), {
        ...data,
        timestamp: data.timestamp || serverTimestamp()
      });
      return { id: docRef.id, success: true };
    } catch (error) {
      console.error(`Error adding document to ${collectionName}:`, error);
      throw error;
    }
  },

  updateDocument: async (collectionName: string, docId: string, data: any) => {
    try {
      const docRef = doc(db, collectionName, docId);
      await setDoc(docRef, {
        ...data,
        lastUpdated: serverTimestamp()
      }, { merge: true });
      return { success: true };
    } catch (error) {
      console.error(`Error updating document in ${collectionName}:`, error);
      throw error;
    }
  },

  getDocuments: async (collectionName: string, filters: any[] = []) => {
    try {
      let q: any = collection(db, collectionName);
      
      // Apply filters if provided
      if (filters.length > 0) {
        const constraints = filters.map(filter => {
          if (filter.operator === '>=') {
            return where(filter.field, '>=', filter.value);
          } else if (filter.operator === '<=') {
            return where(filter.field, '<=', filter.value);
          } else if (filter.operator === '<') {
            return where(filter.field, '<', filter.value);
          } else if (filter.operator === '>') {
            return where(filter.field, '>', filter.value);
          } else if (filter.operator === '==') {
            return where(filter.field, '==', filter.value);
          } else if (filter.operator === '!=') {
            return where(filter.field, '!=', filter.value);
          }
          return where(filter.field, '==', filter.value);
        });
        q = query(q, ...constraints);
      }

      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...(doc.data() as any)
      }));
    } catch (error) {
      console.error(`Error getting documents from ${collectionName}:`, error);
      throw error;
    }
  },

  deleteDocument: async (collectionName: string, docId: string) => {
    try {
      await deleteDoc(doc(db, collectionName, docId));
      return { success: true };
    } catch (error) {
      console.error(`Error deleting document from ${collectionName}:`, error);
      throw error;
    }
  }
};
