import React, { memo } from 'react';
import { Link } from 'react-router-dom';
import { CheckCircle } from 'lucide-react';
import type { Service } from '../types';

interface ServiceCardProps {
  service: Service;
  variant?: 'default' | 'minimal';
}

const ServiceCard: React.FC<ServiceCardProps> = ({
  service,
  variant = 'default'
}) => {
  const IconComponent = service.icon;
  const isHourlyService = service.isHourly;
  
  if (variant === 'minimal') {
    return (
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
        <IconComponent className="h-10 w-10 text-green-700 mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">{service.name}</h3>
        <p className="text-gray-700 text-sm mb-4">{service.description}</p>
        <div className="text-sm text-green-700 font-medium">
          {isHourlyService ? `$${service.rate || service.basePrice}/hour` : `Starting from $${service.basePrice}`}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
      <div className="p-6">
        <div className="flex items-center mb-4">
          <IconComponent className="h-10 w-10 text-green-700 mr-3" />
          <div>
            <h3 className="text-xl font-semibold text-gray-900">{service.name}</h3>
            <p className="text-gray-700 text-sm">{service.description}</p>
          </div>
        </div>

        <div className="space-y-2 mb-6">
          {service.features.map((feature, index) => (
            <div key={index} className="flex items-center text-sm text-gray-700">
              <CheckCircle className="h-4 w-4 text-green-600 mr-2 flex-shrink-0" />
              {feature}
            </div>
          ))}
        </div>

        <div className="flex items-center justify-between">
          <div>
            <div className="text-2xl font-bold text-green-700">
              ${isHourlyService ? (service.rate || service.basePrice) : service.basePrice}{isHourlyService ? '/hr' : ''}
            </div>
            <div className="text-xs text-gray-600">
              {isHourlyService ? 'Hourly rate' : 'Starting price'}
            </div>
          </div>
          <Link 
            to="/quote"
            className="px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors text-center"
          >
            Get Quote
          </Link>
        </div>
      </div>
    </div>
  );
};

ServiceCard.displayName = 'ServiceCard';

export default memo(ServiceCard);
